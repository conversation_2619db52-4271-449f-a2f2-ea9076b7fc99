# Redis Distributed Locking System Design: Comprehensive Plan

**Version:** 1.0
**Date:** June 17, 2025 (Revised)
**Objective:** To provide a semantically consistent, detailed design and implementation plan for the `locking-redis-lock` module, ensuring robust, scalable, and fault-tolerant distributed locking behavior strictly aligned with all user requirements and architectural principles.

## 1. Introduction and Core Principles

This document outlines the refined design and implementation strategy for the Redis-based distributed locking system. It consolidates all architectural decisions, property definitions, operational flows, and implementation details, ensuring strict semantic consistency across all components and documentation. The design prioritizes:

*   **Asynchronous-First Operations:** All core lock operations (`lockAsync`, `tryLockAsync`, `unlockAsync`) are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads.
*   **Atomicity via Lua Scripts:** All critical Redis operations that modify lock state, TTL, or metadata are executed atomically via Lua scripts on the Redis server. This prevents race conditions and ensures data consistency.
*   **Strict Guideline Adherence:** All Redis interactions are channeled exclusively through the `redis-core` module's `ClusterCommandExecutor`, ensuring compliance with a unified Redis interaction layer.
*   **Idempotency:** Every mutating Redis operation is designed to be idempotent, preventing unintended side effects from client retries due to lost responses.
*   **Clear Configuration Hierarchy:** Configuration properties are logically grouped, with a defined override precedence (instance-specific > bucket-level > global defaults).
*   **Robustness:** Comprehensive error handling, precise TTL management, and a resilient watchdog mechanism are central to the design.

---

## 2. Glossary of Key Terms

To ensure absolute clarity and consistency, the following terms are used with precise meanings throughout this document:

*   **`lockKey`**: The primary Redis key for the distributed lock itself (e.g., `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`). This key holds the `ownerId` as its value and has an associated expiry.
*   **`lockDataKey`**: A secondary Redis key (typically a Redis Hash) used to store additional metadata associated with the `lockKey`, such as `ownerId`, `originalLeaseTimeMillis`, and `expiresAtMillis`. Its key format will follow the same lock-type-specific pattern as `lockKey`.
*   **`ownerId`**: A unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). This identifies which application instance and thread/process holds the lock.
*   **`requestUuid`**: A unique identifier (UUID) generated *per logical operation* (e.g., a single `tryLock` call, including its internal retries). This UUID is used for the idempotency mechanism.
*   **`relativeLeaseTimeMillis`**: The duration (in milliseconds) requested by the user for a lock's lease. This is a relative value (e.g., "30 seconds from now"). It is the input to lock acquisition and extension operations.
*   **`originalLeaseTimeMillis`**: The `relativeLeaseTimeMillis` value that was *last explicitly set by a user-initiated lock acquisition or extension*. This value is stored persistently in Redis (`lockDataKey`) and is used by the watchdog to determine the intended full lease duration for renewal cycles. It is **never** modified by the watchdog.
*   **`expiresAtMillis`**: The absolute Unix timestamp (in milliseconds) when the lock is currently set to expire, calculated by the Redis server. This is the value used with `PEXPIREAT` and is stored in Redis (`lockDataKey`).
*   **`safetyBufferMillis`**: A calculated duration (`watchdog.interval * watchdog.factor`) used by the watchdog. It determines the minimum `userLeaseTime` for watchdog eligibility and the target TTL the watchdog aims to maintain for eligible locks.
*   **Idempotency Wrapper**: A pattern applied to all mutating Lua scripts. It involves checking a Redis response cache using a `requestUuid` before executing the core logic, and storing the operation's result in the cache with `responseCacheTtl` upon completion.
*   **Lock-Type Segment**: A mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`) that semantically isolates different lock types, preventing cross-type interference.

---

## 3. Configuration Properties (`RedisLockProperties.java`)

The `RedisLockProperties` class serves as the central configuration point for the `locking-redis-lock` module, defining global defaults and nested configurations. It is structured to allow clear override precedence: **Instance-specific settings (via builders) > Programmatic Bucket Configuration (via builders) > Global Configuration (YAML/Java defaults)**.

```mermaid
graph TD
    A[RedisLockProperties
    (destilink.fw.locking.redis.*)] --> B[enabled: boolean]
    A --> C[stateKeyExpiration: Duration]
    A --> D[responseCacheTtl: Duration]
    A --> E[maxLockNameLength: int]
    A --> F[maxBucketNameLength: int]
    A --> G[maxScopeLength: int]
    A --> H[lockOwnerIdValidationRegex: String]
    A --> I[watchdog: WatchdogProperties]
    A --> J[defaults: Defaults]
    A --> K[retryExecutorSupplier: Supplier<ScheduledExecutorService>]

    subgraph WatchdogProperties [WatchdogProperties
    (destilink.fw.locking.redis.watchdog.*)]
        I --> I1[interval: Duration]
        I --> I2[factor: double]
        I --> I3[corePoolSize: int]
        I --> I4[threadNamePrefix: String]
        I --> I5[shutdownAwaitTermination: Duration]
    end

    subgraph Defaults [Defaults
    (destilink.fw.locking.redis.defaults.*)]
        J --> J1[leaseTime: Duration]
        J --> J2[retryInterval: Duration]
        J --> J3[maxRetries: int]
        J --> J4[acquireTimeout: Duration]
    end
```

### 3.1 Top-Level Properties (`destilink.fw.locking.redis.*`)

*   **`enabled` (boolean)**: Master switch to enable/disable the entire Redis locking feature.
*   **`stateKeyExpiration` (Duration)**: TTL for auxiliary state keys in Redis (e.g., for `StateLock`).
*   **`responseCacheTtl` (Duration)**: Defines the TTL for idempotency records in Redis. These records (keyed by `requestUuid`) ensure that retried operations are not executed multiple times if the original attempt succeeded. **This property is critical for idempotency.**
*   **`lockOwnerIdValidationRegex` (String)**: A regex pattern used to validate the format of `ownerId` strings. **(Note: This property will be removed and the regex hardcoded in `LockOwnerSupplier.java`.)**
*   **`maxLockNameLength` (int)**: Maximum allowed length for lock names. **(Note: This property will be removed.)**
*   **`maxBucketNameLength` (int)**: Maximum allowed length for bucket names. **(Note: This property will be removed.)**
*   **`maxScopeLength` (int)**: Maximum allowed length for scope names. **(Note: This property will be removed.)**
*   **`watchdog` (WatchdogProperties)**: Nested properties for the system-level watchdog.
*   **`defaults` (Defaults)**: Nested properties for overridable lock implementation defaults.
*   **`retryExecutorSupplier` (Supplier<ScheduledExecutorService>)**: Provides a dedicated `ScheduledExecutorService` primarily for scheduling delayed tasks within `CompletableFuture` chains (e.g., for internal retries with `retryInterval`). This is an internal mechanism and not a user-configurable YAML property.

### 3.2 Nested Properties: `WatchdogProperties` (`destilink.fw.locking.redis.watchdog.*`)

These properties configure the system-level `LockWatchdog`. The watchdog service is **always active** if the `locking-redis-lock` module is enabled. Its application to individual locks is conditional.

*   **`interval` (Duration)**: Interval at which the watchdog checks and attempts to extend the lease for monitored locks. This is an independent, configurable duration.
*   **`factor` (double)**: Multiplier for calculating the `safetyBufferMillis` (`interval * factor`). Only locks with `userLeaseTime > safetyBufferMillis` are monitored by the watchdog.
*   **`corePoolSize` (int)**: Core thread pool size for the watchdog's `ScheduledExecutorService`.
*   **`threadNamePrefix` (String)**: Prefix for watchdog executor threads for better observability.
*   **`shutdownAwaitTermination` (Duration)**: Timeout for graceful shutdown of the watchdog's scheduler.

### 3.3 Nested Properties: `Defaults` (`destilink.fw.locking.redis.defaults.*`)

These properties define default values for lock implementation behavior. They can be overridden by lock or factory builders.

*   **`leaseTime` (Duration)**: Default duration a lock will be held if not explicitly specified.
*   **`retryInterval` (Duration)**: Default wait time between retry attempts for *individual Redis operations* within `RedisLockOperationsImpl` if a transient error occurs.
*   **`maxRetries` (int)**: Default maximum number of retry attempts for *individual Redis operations* within `RedisLockOperationsImpl`.
*   **`acquireTimeout` (Duration)**: Default overall timeout for a lock acquisition operation (e.g., `tryLock(long time, TimeUnit unit)`). This is an approximate limit for the entire application-level lock attempt.

---

## 4. Key Management and Semantic Isolation

To ensure semantic isolation, prevent accidental cross-type operations, and enable future extensibility, all Redis keys for locks will include a lock-type-specific segment.

### 4.1 Redis Key Format

All main lock keys and associated metadata keys will follow this format:
`<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`

*   **`<prefix>`**: Configured application-wide prefix (e.g., `myApp`).
*   **`<bucketName>`**: Logical grouping for locks (e.g., `orders`).
*   **`__locks__`**: A fixed segment indicating a lock-related key.
*   **`<lockType>`**: A mandatory, stable, documented string identifying the lock type (e.g., `reentrant`, `stamped`, `state`).
*   **`{<lockName>}`**: The actual name of the lock. The curly braces ensure Redis Cluster co-location for all keys related to this specific lock, allowing atomic multi-key Lua script operations.

**Examples:**
*   Reentrant Lock: `myApp:__lock_buckets__:orders:__locks__:reentrant:{order123}`
*   Stamped Lock: `myApp:__lock_buckets__:orders:__locks__:stamped:{order123}`
*   State Lock: `myApp:__lock_buckets__:orders:__locks__:state:{order123}`

### 4.2 Key Construction and Usage

*   **Java Code**: All Java code responsible for constructing Redis keys (e.g., in `LockBucket.java`, lock factories, key utilities) must be updated to require a lock type identifier.
*   **Lua Scripts**: All Lua scripts must expect and use this new key format.
*   **Other Key Types**: All related keys (e.g., `lockDataKey` for metadata, response cache keys, unlock Pub/Sub channel names) will also incorporate the lock type segment for maximum clarity and isolation.

### 4.3 No Migration Required

This change is forward-compatible. Existing keys in Redis will remain as they are; new keys for new lock types will use the new format. No data migration is performed or required.

---

## 5. Lock Acquisition and Lifecycle Management

The lock acquisition process is designed for high performance, reliability, and clear timeout semantics.

### 5.1 Asynchronous-First Design with Virtual Threads

All core lock operations (`lock()`, `tryLock()`, `unlock()`, and their `Async` variants) will leverage Java Virtual Threads.

```mermaid
flowchart TD
    A[User Call: lock.lock() / tryLock() / unlock()] --> B{Dispatch Task to Virtual Thread Executor}
    B --> C[Capture MDC Context]
    C --> D[Submit Task to virtualThreadExecutor]
    D -- Synchronous Call --> E[Block on CompletableFuture.get()
    (User thread parks, Virtual Thread runs)]
    D -- Asynchronous Call --> F[Return CompletableFuture to User]

    subgraph VirtualThreadExecution [Task Execution on Virtual Thread]
        VT1[Restore MDC Context] --> VT2[Execute Lock/Unlock Logic]
        VT2 --> VT3{Redis I/O or Wait?}
        VT3 -- Yes --> VT4[Perform Non-Blocking I/O
        (e.g., ClusterCommandExecutor.executeScript)]
        VT4 --> VT5[Wait on CompletableFuture / Thread.sleep()
        (Virtual Thread parks)]
        VT5 --> VT3
        VT3 -- No --> VT6[Complete CompletableFuture]
        VT6 --> VT7[Clear MDC Context]
    end
    E --> VT1
    F --> VT1
```

*   **Immediate Handoff**: Public lock operation methods will immediately dispatch the actual work to a task executed on a dedicated Virtual Thread. This frees the calling platform thread as quickly as possible.
*   **MDC Propagation**: The MDC context from the calling thread will be captured and propagated to the Virtual Thread, ensuring consistent logging.
*   **Thread.sleep()**: `Thread.sleep()` is used for delays (e.g., `retryInterval`) within the Virtual Thread, as it efficiently parks the Virtual Thread without blocking a platform thread.
*   **Avoiding Pinning**: `synchronized` blocks that span I/O or parking operations must be avoided. `java.util.concurrent.locks.ReentrantLock` should be used for internal critical sections if necessary.

### 5.2 Lock Acquisition Flow

The acquisition loop is designed to be robust, leveraging Pub/Sub notifications and TTL-based fallbacks, all while respecting the overall `acquireTimeout`.

```mermaid
flowchart TD
    A[Start: Lock Acquisition Attempt] --> B[Get/Create LockSemaphoreHolder for lockKey]
    B --> C[Register LockSemaphoreHolder with UnlockMessageListenerManager
    (CRITICAL: BEFORE first Redis attempt to prevent race conditions)]
    C --> D{Loop: Until Acquired, Timeout, or Interrupted}
    D --> E[Attempt to Acquire Lock via Lua Script
    (RedisLockOperations.tryAcquireLockInternalAsync)]
    E --> F{Lua Script Result?}

    F -- "Lock Acquired (e.g., returns < 0)" --> G[Complete Lock Future Successfully]
    F -- "Lock Held by Other (e.g., returns >= 0 with TTL)" --> H[Get current TTL from script response]
    H --> I[Calculate Wait Duration: min(Returned TTL, Configured Retry Interval)]
    I --> J[Wait on Registered LockSemaphoreHolder / CompletableFuture for signal or wait Duration to elapse]
    J -- "Signal Received (Pub/Sub)" --> D
    J -- "Wait Duration Elapsed (Timeout)" --> D
    J -- "Overall acquireTimeout Reached" --> K[Fail with LockAcquisitionTimeoutException]
    J -- "Redis-related Exception" --> L[Propagate Specific Exception]

    G --> M[Register with LockWatchdog (if eligible)]
    M --> N[Operation Complete]

    K --> N
    L --> N
```

*   **Registration First**: The `LockSemaphoreHolder` (or its `CompletableFuture` equivalent) **must be obtained/created and registered with `UnlockMessageListenerManager` *before* any initial attempt to acquire the lock via Redis.** This prevents race conditions where an unlock Pub/Sub message might be missed.
*   **Primary Waiting Mechanism**: Relies on Redis Pub/Sub for unlock notifications. `UnlockMessageListener` signals the `LockSemaphoreHolder`.
*   **Fallback Timeout**: If a Pub/Sub message is not received, the waiting mechanism times out based on `min(Returned TTL from script, Configured Retry Interval)`. Upon timeout, a new lock acquisition attempt is made.
*   **`acquireTimeout` Nuances**:
    *   For `tryLock` variants, `acquireTimeout` governs the *entire* operation.
    *   **Non-Interruption of In-Flight Operations**: `acquireTimeout` MUST NOT interrupt a Redis command already dispatched to and in-flight within `ClusterCommandExecutor`.
    *   **Redis Operation Result Precedence**: The result (success, failure, or specific exception) of an in-flight Redis operation takes precedence over a concurrently expiring `acquireTimeout`. If a Redis operation successfully acquires the lock, the lock is acquired, even if `acquireTimeout` notionally passed *during* that final Redis call.
    *   **`TimeoutException` Condition**: A `TimeoutException` (or equivalent) due to `acquireTimeout` should only be raised if the deadline is passed, the latest Redis operation (if any) has completed, and that operation did *not* acquire the lock AND did *not* result in a Redis-specific exception.

### 5.3 Scope of `maxRetries`

*   The `defaults.maxRetries` property (from `RedisLockProperties.Defaults`) is specifically for retrying *individual failing Redis operations* within `RedisLockOperationsImpl` (e.g., a Lua script execution that fails due to a transient network issue).
*   It is **NOT** a limit on the number of attempts to acquire a lock that is busy (held by another owner). The overall lock acquisition loop for a busy lock is governed by `acquireTimeout` (for timed attempts) or waits indefinitely (for blocking `lock()` calls), relying on Pub/Sub and TTL-based fallbacks.

### 5.4 Lua Script Return Value Conventions

All lock acquisition and status-checking Lua scripts will adhere to a standardized return convention:

*   **Return Value `>= 0`**: The lock is already held by another client.
    *   `0`: The lock is held "forever" (no TTL).
    *   `> 0`: The value represents the remaining TTL (in milliseconds) or the absolute Unix timestamp (in milliseconds) when the lock will expire. The specific meaning (TTL vs. Unix timestamp) will be consistently documented with the script.
*   **Return Value `< 0`**: The lock was acquired successfully.
    *   The absolute value (`abs(result)`) represents the TTL (in milliseconds) or the absolute Unix timestamp (in milliseconds) when the newly acquired lock will expire. The specific meaning will be consistently documented with the script.

All Lua scripts will also return a structured response (e.g., a Redis array) that includes:
*   A status code (as described above).
*   The `expiresAtMillis` (absolute Unix timestamp calculated by Redis server).
*   The `originalLeaseTimeMillis` (the user's intended relative lease duration, as stored in Redis).

### 5.5 Lua-Only Redis Operations

To guarantee atomicity, consistency, and idempotency, **all Redis operations related to lock state, TTL, expireAt, or any lock metadata MUST be performed exclusively via Lua scripts.**

*   **Strict Prohibition**: Direct usage of Redis operations (e.g., `SET`, `PEXPIRE`, `HSET`, `DEL`) from Java code is **STRICTLY PROHIBITED**.
*   **Refactoring**: Any existing direct Redis calls (e.g., `RedisStampedLock.java`'s `updateLockState` method) must be refactored to go through `RedisLockOperationsImpl` which then executes the appropriate Lua script.

---

## 6. Idempotency Mechanism

Idempotency is critical for distributed locking, especially when client retries occur due to network issues or lost responses.

### 6.1 Core Mechanism

*   **Unique Request UUID**: For every logical operation (e.g., a single `tryLock` call, including its internal application-level retries), a unique `requestUuid` is generated. This UUID identifies that specific logical attempt.
*   **Response Cache**: Lua scripts use a dedicated "response cache" in Redis.
    1.  **Check First**: Before executing its core logic, the Lua script checks this cache for the `requestUuid`.
    2.  **Serve Cached**: If a response is found for that `requestUuid`, it means the operation was already successfully completed, and the cached response is immediately returned, preventing re-execution.
    3.  **Execute & Cache**: If no response is found, the core operation proceeds. Upon successful completion, its result is stored in the response cache (keyed by `requestUuid`) with a `responseCacheTtl`.

```mermaid
flowchart TD
    A[Client Initiates Lock/Unlock Operation] --> B[RedisLockOperationsImpl]
    B --> C[Generate Unique requestUuid]
    C --> D[Call Lua Script (e.g., acquire_lock.lua)]

    subgraph LuaScriptExecution [Lua Script on Redis Server]
        LS1[Receive requestUuid, responseCacheTtl] --> LS2{Check Response Cache for requestUuid?}
        LS2 -- "Found" --> LS3[Return Cached Result]
        LS2 -- "Not Found" --> LS4[Execute Core Lock/Unlock Logic]
        LS4 --> LS5{Operation Successful?}
        LS5 -- "Yes" --> LS6[Store Result in Response Cache (requestUuid, responseCacheTtl)]
        LS6 --> LS7[Return Actual Result]
        LS5 -- "No" --> LS7
    end
    D --> LS1
    LS3 --> E[Return Result to Client]
    LS7 --> E
```

### 6.2 Responsibilities

*   **`RedisLockOperationsImpl.java`**:
    *   Is the **single point of responsibility** for generating the `requestUuid` *per logical operation* and ensuring it's passed to Lua scripts.
    *   Ensures the `responseCacheTtl` (from `RedisLockProperties`) is also passed to Lua scripts.
    *   If the method implements application-level retries (using `defaults.maxRetries` and `defaults.retryInterval`), the *same generated `requestUuid`* must be used for all retry attempts of that single logical operation.
*   **Lua Scripts**: Must implement the idempotency check and response cache logic as described, for **every script that mutates lock state, TTL, or metadata**. This is mandatory.
*   **`RedisLockProperties.responseCacheTtl`**: Defines the TTL for these idempotency records. It must be long enough to cover typical client retry windows.

---

## 7. Watchdog Mechanism

The `LockWatchdog` ensures that long-lived locks are automatically extended, preventing premature expiration.

### 7.1 Always Active, Conditional Per Lock

*   The `LockWatchdog` Spring bean is **always active** if the `locking-redis-lock` module is enabled. There is no separate `watchdog.enabled` property.
*   The watchdog only monitors locks that are:
    1.  **Application-Instance-Bound**: Determined by `LockOwnerSupplier.canUseWatchdog(lockKey, ownerId)` returning `true`. This means the lock is owned by the current application instance.
    2.  **Sufficiently Long-Lived**: The `userProvidedLeaseTime` for the lock must be greater than the `safetyBufferMillis`.

### 7.2 `safetyBuffer` Calculation and Initial Lock TTL

*   **`safetyBufferMillis`**: Calculated using the explicit formula:
    ```
    safety_buffer_ms = MAX(MIN_SAFETY_BUFFER_MS, original_lease_time_ms * SAFETY_FACTOR)
    where:
    - MIN_SAFETY_BUFFER_MS = 500 (minimum safety margin)
    - SAFETY_FACTOR = 0.2 (20% of original lease time)
    - original_lease_time_ms = user-specified lease duration
    ```
    This ensures a minimum safety margin while scaling appropriately with longer lease times.
*   **Initial Lock Acquisition**:
    *   **If Watchdog NOT Used** (e.g., `userLeaseTime <= safetyBufferMillis` or not instance-bound): The lock is acquired in Redis with the full `userProvidedLeaseTime` as its initial TTL.
    *   **If Watchdog IS Used** (`userLeaseTime > safetyBufferMillis` and instance-bound): The lock is acquired in Redis with an initial TTL of `safetyBufferMillis`. The lock is then registered with the `LockWatchdog` with its `originalLeaseTimeMillis` (the user's full intended lease).

### 7.3 Watchdog Refresh Logic

The watchdog periodically extends the lease of monitored locks without altering the `originalLeaseTimeMillis`.

```mermaid
flowchart TD
    A[LockWatchdog: Scheduled Task (every 'interval')] --> B{For Each Monitored LockInfo}
    B --> C[Retrieve LockInfo (ownerId, userIntendedExpireTimeMillis)]
    C --> D[Calculate remainingUserTimeMillis = userIntendedExpireTimeMillis - currentTime]

    D -- "remainingUserTimeMillis <= 0" --> E[Lock Expired (User Intent)]
    E --> F[Unregister Lock from Watchdog]
    E --> G[Log Warning if Lock Still Held]

    D -- "0 < remainingUserTimeMillis <= safetyBufferMillis" --> H[Final Leg: Lock Approaching User's Final Expiry]
    H --> I[Call Lua Script (watchdog_refresh_lock.lua) with userIntendedExpireTimeMillis as PEXPIREAT target]
    I --> J[Update LockInfo.currentAbsoluteExpiresAtMillis]
    I --> K[Unregister Lock from Watchdog (or mark for cleanup)]

    D -- "remainingUserTimeMillis > safetyBufferMillis" --> L[Standard Operation: Maintain Safety Buffer]
    L --> M[Call Lua Script (watchdog_refresh_lock.lua) with (currentTime + safetyBufferMillis) as PEXPIREAT target]
    M --> N[Update LockInfo.currentAbsoluteExpiresAtMillis]

    F --> P[Continue to next monitored lock]
    K --> P
    N --> P
```

*   **`LockInfo`**: Stores `ownerId`, `userIntendedExpireTimeMillis` (the `originalLeaseTimeMillis` from registration), and `currentAbsoluteExpiresAtMillis`.
*   **Refresh Strategy**:
    *   **Standard Operation**: If `remainingUserTimeMillis > safetyBufferMillis`, the watchdog extends the lock in Redis using `PEXPIREAT (currentTimeMillis + safetyBufferMillis)`. This maintains the `safetyBuffer` TTL.
    *   **Final Leg**: If `remainingUserTimeMillis <= safetyBufferMillis` (but > 0), the watchdog extends the lock using `PEXPIREAT userIntendedExpireTimeMillis`. This ensures the lock expires precisely when the user originally intended. After this, the lock is unregistered from the watchdog.
*   **`originalLeaseTimeMillis` Preservation**: The watchdog **never** overwrites the `originalLeaseTimeMillis` stored in Redis. It only uses it to calculate the target `expiresAtMillis` for renewal.
*   **Redis Time**: All expiry calculations in Lua scripts use `redis.call('TIME')` for precision.

### 7.4 User-Initiated Lock Extension

*   When a user explicitly calls `extendLease(newRelativeLeaseTime)`:
    *   The `newRelativeLeaseTime` becomes the *new* `originalLeaseTimeMillis` for that lock. This value is updated in Redis (`lockDataKey`).
    *   The lock's TTL in Redis is immediately set to `min(newRemainingUserLeaseTime, safetyBufferMillis)` (if watchdog-managed and eligible) or `newRelativeLeaseTime` (if not watchdog-managed).
    *   The watchdog registration for this lock is updated (`lockWatchdog.updateRegistration`) to reflect the new `userIntendedExpireTimeMillis` and `currentAbsoluteExpiresAtMillis`.

---

## 8. Error Handling and Robustness

Robust error handling is crucial for distributed systems.

### 8.1 Custom Exception Hierarchy

A well-defined exception hierarchy will provide clear, actionable error information. All lock-related exceptions will extend a common base, e.g., `AbstractRedisLockException`.

*   **`LockAcquisitionTimeoutException`**: Thrown when `acquireTimeout` is reached and the lock could not be acquired.
*   **`LockOperationException`**: General exception for failures during lock operations (e.g., Redis errors, script errors).
*   **`RetryableLockException`**: Indicates a transient error that can be retried (e.g., temporary network issue).
*   **`NonRetryableLockException`**: Indicates a permanent error that should not be retried (e.g., invalid arguments, authentication failure).
*   **`LockInterruptedException`**: Thrown if a thread is interrupted while waiting for a lock.

### 8.2 Exception Handling in `RedisLockOperationsImpl`

*   `RedisLockOperationsImpl` is responsible for catching exceptions from `ClusterCommandExecutor` / Lua script executions.
*   It will map these low-level exceptions to the defined custom exception hierarchy.
*   **Internal Retries**: For *individual Redis operations*, `RedisLockOperationsImpl` will implement retry logic using `defaults.maxRetries` and `defaults.retryInterval` for `RetryableLockException` types.
*   **Immediate Propagation**: `NonRetryableLockException` types are propagated immediately without retries.
*   **MDC Context**: All exceptions logged or thrown will carry relevant MDC information (lock key, owner ID, request UUID) for better traceability.

### 8.3 `acquireTimeout` Precedence

*   The `defaults.acquireTimeout` is an *approximate* overall limit for the user-facing lock acquisition attempt (`tryLock`).
*   **Non-Interruption**: If `acquireTimeout` expires while a Redis command is already in flight (dispatched to `ClusterCommandExecutor`), that command **MUST NOT be interrupted** by the `locking-redis-lock` module. It must be allowed to complete or time out based on its own (Lettuce/`ClusterCommandExecutor`) lower-level timeout settings.
*   **Result Precedence**: The actual result of the in-flight Redis operation takes precedence.
    *   If the Redis operation successfully acquires the lock, the lock is considered acquired, even if `acquireTimeout` passed during that specific Redis call.
    *   If the Redis operation fails with a Redis-specific exception (e.g., connection issue, script error, after exhausting its own `maxRetries` within `RedisLockOperationsImpl`), that specific exception MUST be propagated.
*   **`TimeoutException` Condition**: A `LockAcquisitionTimeoutException` (or equivalent) should only be raised if the `acquireTimeout` deadline is passed, AND the latest Redis operation (if any) has completed *without* acquiring the lock, AND *without* resulting in a Redis-specific exception.

---

## 9. Affected Components and Implementation Plan

This section outlines the step-by-step implementation plan, detailing changes to specific files and the rationale behind them.

### Step 9.1: Remove `pubSubWaitTimeout`

*   **Rationale**: Redundant with `acquireTimeout` and internal TTL/retry logic.
*   **Affected Files**:
    *   `framework-modules/locking/modules/locking-redis-lock/src/main/java/com/tui/destilink/framework/locking/redis/lock/config/RedisLockProperties.java` (Property definition)
*   **Actions**: Delete `pubSubWaitTimeout` field, getter, setter, Javadoc, and any associated annotations.

### Step 9.2: Refactor `requestUuid` Handling and Centralize Idempotency

*   **Rationale**: Centralize `requestUuid` generation and idempotency logic in `RedisLockOperationsImpl` for robustness and clear separation of concerns. Ensure all mutating Redis operations are idempotent.
*   **Affected Files**:
    *   `RedisLockProperties.java` (Javadoc for `responseCacheTtl`)
    *   `AbstractRedisLock.java` (Remove `requestUuid` field and related logic)
    *   `RedisLockOperationsImpl.java` (Centralize `requestUuid` generation, pass to Lua, use `responseCacheTtl`)
    *   Lua scripts (`.lua` files) (Implement idempotency wrapper for *all* mutating scripts)
*   **Lua Script Method Signatures**:
    * `acquire_lock.lua`: `KEYS[1]=lockKey, ARGV[1]=owner, ARGV[2]=leaseTimeMs, ARGV[3]=requestUuid`
    * `unlock_lock.lua`: `KEYS[1]=lockKey, KEYS[2]=channelKey, ARGV[1]=owner, ARGV[2]=requestUuid`
    * `extend_lock.lua`: `KEYS[1]=lockKey, ARGV[1]=owner, ARGV[2]=newLeaseTimeMs, ARGV[3]=requestUuid`
    * `watchdog_refresh_lock.lua`: `KEYS[1]=lockKey, ARGV[1]=owner, ARGV[2]=safetyBufferMs, ARGV[3]=requestUuid`
    * `acquire_reentrant_lock.lua`: `KEYS[1]=lockKey, ARGV[1]=owner, ARGV[2]=leaseTimeMs, ARGV[3]=requestUuid`
    * `release_reentrant_lock.lua`: `KEYS[1]=lockKey, KEYS[2]=channelKey, ARGV[1]=owner, ARGV[2]=requestUuid`
*   **Actions**:
    1.  Update Javadoc for `responseCacheTtl` in `RedisLockProperties.java` to explain its idempotency role.
    2.  Remove `requestUuid` field, its initialization, and `getRequestUuid()` from `AbstractRedisLock.java`. Adjust calls to exception constructors.
    3.  In `RedisLockOperationsImpl.java`:
        *   For each public method representing a logical Redis operation requiring idempotency (e.g., `tryLock`, `unlock`, `extendLease`, `updateState`, `watchdogRefreshLease`), generate a new, unique `requestUuid` internally upon the initial call.
        *   Use this *same `requestUuid`* for all application-level retry attempts of that single logical operation.
        *   Pass `requestUuid` and `responseCacheTtl` to the corresponding Lua script(s).
    4.  Verify (conceptually, then practically via testing) that all Lua scripts correctly implement the idempotency check and response cache logic using `requestUuid` and `responseCacheTtl`. **Idempotency is mandatory for all mutating scripts.**

### Step 9.3: Adopt Virtual Threads for Lock Operations and Refine Retry/Wait Mechanics

*   **Rationale**: Enhance scalability and throughput by using Virtual Threads for all I/O-bound and waiting lock operations, preventing platform thread blockage. Refine retry and waiting mechanics.
*   **Affected Files**:
    *   `RedisLockAutoConfiguration.java` (Define a virtual thread executor bean)
    *   `AbstractRedisLock.java` (Initiate tasks on virtual threads, handle `CompletableFuture` blocking for sync calls)
    *   `RedisLockOperationsImpl.java` (Implement retry logic using `Thread.sleep()` in VT, manage waiting logic)
    *   `LockSemaphoreHolder.java`, `UnlockMessageListener.java` (Clarify roles for `CompletableFuture`-based waits)
    *   `core` module utilities (for MDC propagation)
*   **Actions**:
    1.  Define a Spring bean for `ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();`.
    2.  Modify public lock operation methods in `AbstractRedisLock.java` and its hierarchy to:
        *   Capture MDC context.
        *   Create a task encapsulating lock/unlock logic.
        *   Submit task to `virtualThreadExecutor`.
        *   Synchronous methods will block on the returned `Future`. Asynchronous methods will return the `CompletableFuture`.
    3.  Implement robust MDC context propagation to Virtual Threads (using existing `core` utilities or a new wrapper).
    4.  In `RedisLockOperationsImpl.java`:
        *   Implement application-level retry logic for *individual Redis command executions* (using `defaults.maxRetries` and `defaults.retryInterval`) directly within the Virtual Thread's task, using `Thread.sleep()` for delays.
        *   Orchestrate the lock acquisition loop (for busy locks) to:
            *   Register `LockSemaphoreHolder` *before* the first attempt.
            *   Wait on the `CompletableFuture` from `LockSemaphoreHolder` with a timeout of `min(Returned TTL, Configured Retry Interval)`.
            *   Respect `acquireTimeout` nuances (non-interruption of in-flight ops, result precedence).
    5.  Review and adapt `LockSemaphoreHolder.java` and `UnlockMessageListener.java` to work seamlessly with `CompletableFuture`-based waits managed by Virtual Threads, ensuring correct registration order.
    6.  **Remove `pubSubWaitTimeout` from `RedisLockProperties.java` and any usage**: This property is now fully obsolete as `acquireTimeout` and the refined internal wait/retry logic cover all timeout aspects.

### Step 9.4: Refine Watchdog Mechanism and Configuration

*   **Rationale**: Overhaul `LockWatchdog` to be always active, conditionally monitor locks based on `safetyBuffer`, use `PEXPIREAT`, and consistently manage `expiresAtMillis` and `originalLeaseTimeMillis` in Redis.
*   **Affected Files**:
    *   `RedisLockProperties.java` (`WatchdogProperties` inner class)
    *   `RedisLockAutoConfiguration.java` (Remove conditional watchdog bean)
    *   `LockOwnerSupplier.java` (Javadoc for `canUseWatchdog`)
    *   `AbstractRedisLock.java` (Watchdog eligibility, initial TTL setting, registration)
    *   `RedisLockOperationsImpl.java` (New method signatures for structured returns, call Lua scripts)
    *   `LockWatchdog.java` (Major logic changes for `extendLocks`, `LockInfo`, `originalLeaseTimeMillis` handling)
    *   Lua scripts (`acquire_lock.lua`, `extend_lock.lua`, `watchdog_refresh_lock.lua`, `check_lock_status.lua`)
*   **Actions**:
    1.  Modify `RedisLockProperties.WatchdogProperties`:
        *   Ensure `interval` is an independent `Duration`.
        *   Add `factor` (double) property.
        *   Remove `operationMaxRetries` and `operationTimeout` (these are now handled by `Defaults.maxRetries` and `Defaults.retryInterval` via `RedisLockOperationsImpl`).
    2.  Update `RedisLockAutoConfiguration.java`: Remove `@ConditionalOnProperty` from `lockWatchdog` bean definition.
    3.  Update `LockOwnerSupplier.java` Javadoc for `canUseWatchdog`.
    4.  Refactor `AbstractRedisLock.java`:
        *   Calculate `safetyBufferMillis = watchdogProperties.getInterval().toMillis() * watchdogProperties.getFactor()`.
        *   Only register with watchdog if `LockOwnerSupplier.canUseWatchdog()` is true **and** `userLeaseTime > safetyBufferMillis`.
        *   If watchdog NOT used, acquire lock with full `userLeaseTime`. If IS used, acquire with initial TTL of `safetyBufferMillis`.
        *   In unlock, always `lockWatchdog.unregisterLock()`.
        *   In `extendLeaseAsync`, update watchdog registration with `newExpiresAtMillis` and `newOriginalLeaseTimeMillis`.
    5.  Overhaul `RedisLockOperationsImpl.java`:
        *   Modify method signatures to return structured objects (`LockOperationResult`) containing `acquired` status, `expiresAtMillis`, and `originalLeaseTimeMillis`.
        *   Ensure all methods pass the user-provided lease time to Lua scripts.
        *   Parse multi-value responses from Lua scripts.
    6.  Modify Lua Scripts (`acquire_lock.lua`, `extend_lock.lua`, `watchdog_refresh_lock.lua`, `check_lock_status.lua`):
        *   **Idempotency is mandatory**: Every script uses `requestUuid` and `responseCacheTtl` for caching.
        *   All `expiresAtMillis` calculations use `redis.call('TIME')`.
        *   Lock data (including `ownerId`, `expiresAtMillis`, `originalLeaseTimeMillis`) is stored in a Redis Hash (`lockDataKey`).
        *   Main lock key is set with `PEXPIREAT`.
        *   **`acquire_lock.lua`**: Returns `{status_code, expiresAtMillis, originalLeaseTimeMillis}`.
        *   **`extend_lock.lua` (user-initiated)**: Updates `originalLeaseTimeMillis` in Hash.
        *   **`watchdog_refresh_lock.lua`**: Reads `originalLeaseTimeMillis` from Hash (does NOT change it). Recalculates `newExpiresAtMillis` and updates `expiresAtMillis` in Hash and lock key.
    7.  Overhaul `LockWatchdog.java`:
        *   `LockInfo` stores `ownerId`, `userOriginalLeaseTimeMillis`, `currentAbsoluteExpiresAtMillis`.
        *   `extendLocks()` logic:
            *   If `remainingUserTimeMillis <= 0`, unregister.
            *   If `0 < remainingUserTimeMillis <= safetyBufferMillis`, extend with `PEXPIREAT userIntendedExpireTimeMillis`. Unregister.
            *   If `remainingUserTimeMillis > safetyBufferMillis`, extend with `PEXPIREAT (currentTime + safetyBufferMillis)`.
            *   Update `lockInfo.currentAbsoluteExpiresAtMillis`. `originalLeaseTimeMillis` in `LockInfo` remains unchanged by watchdog.

### Step 9.5: Enforce Lock-Type-Specific Redis Key Suffixes

*   **Rationale**: Guarantee semantic isolation, prevent cross-type operations, simplify Lua, enable extensibility.
*   **Affected Files**: All key construction logic in Java, all Lua scripts, `redis_key_schema.md`.
*   **Actions**:
    1.  Enforce key format: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`.
    2.  Update all Java code to require `lockType` for key construction.
    3.  Update all Lua scripts to expect and use this format.
    4.  Ensure all related keys (state, response cache, unlock channels) also include `lockType`.

### Step 9.6: Standardize Lua Script Return Value Conventions

*   **Rationale**: Unambiguously communicate lock status and TTL/expireAt semantics from Lua.
*   **Affected Files**: All Lua scripts, `RedisLockOperationsImpl.java`, `AbstractRedisLock.java`.
*   **Actions**:
    1.  All lock acquisition scripts return:
        *   `>= 0`: Lock already held (value is TTL/Unix timestamp; 0 for forever).
        *   `< 0`: Lock acquired successfully (abs(result) is TTL/Unix timestamp).
    2.  All Lua scripts return a structured response (e.g., Redis array) including `status_code`, `expiresAtMillis`, and `originalLeaseTimeMillis`.
    3.  Java code interprets these return values consistently.

### Step 9.7: Enforce Lua-Only Redis Operations

*   **Rationale**: Guarantee atomicity and consistency; all lock state/TTL/metadata changes via Lua scripts only.
*   **Affected Files**: All Java code (audit `RedisStampedLock.java` and similar).
*   **Actions**:
    1.  Audit all Java code for direct Redis operations (for lock state, TTL, expireAt, metadata).
    2.  Refactor any found direct operations to use Lua scripts via `RedisLockOperationsImpl`.

### Step 9.8: Refine `RedisLockProperties` (Final Property Clean-up)

*   **Rationale**: Remove unnecessary properties and hardcode regex for simplicity and clarity.
*   **Affected Files**: `RedisLockProperties.java`, `LockOwnerSupplier.java`.
*   **Actions**:
    1.  Remove `lockOwnerIdValidationRegex`, `maxLockNameLength`, `maxBucketNameLength`, `maxScopeLength` from `RedisLockProperties.java`.
    2.  Hardcode the regex pattern `^[a-zA-Z0-9_-]+(:[a-zA-Z0-9_-]+)*$` as a static final `Pattern` in `LockOwnerSupplier.java`.
    3.  Remove `fairLockBehavior` and `asyncExecutorName` from `RedisLockProperties.Defaults`.
    4.  Remove `redisOperationTimeout` from `RedisLockProperties.Defaults`.
    5.  Update Javadoc for `RedisLockProperties.Defaults.acquireTimeout` to clarify its scope (overall operation, non-interrupting in-flight Redis commands).

### Step 9.9: Comprehensive Documentation Updates

*   **Rationale**: Ensure absolute consistency between implemented code and ALL framework documentation. Prevent user confusion, outdated information, and potential misconfiguration.
*   **Affected Files**:
    *   `framework-modules/locking/modules/locking-redis-lock/docs/configuration.md`
    *   `framework-modules/locking/modules/locking-redis-lock/docs/lock_acquisition.md` (Mermaid diagrams)
    *   `framework-modules/locking/modules/locking-redis-lock/docs/watchdog.md`
    *   `framework-modules/locking/modules/locking-redis-lock/docs/redis_key_schema.md`
    *   All Javadoc within `locking-redis-lock` module.
    *   **All 17 markdown files** within `c:\\Users\\<USER>\\Desktop\\destilink-framework\\docs\\` and its subdirectories.
*   **Actions**:
    1.  **Read and Semantically Analyze**: For each of the 17 general framework markdown files, read thoroughly to understand its purpose, context, and any references (explicit, implicit, or semantic) to locking mechanisms, timeout strategies, configuration parameters, or examples.
    2.  **Identify and Update References**:
        *   Perform textual searches for all removed properties (`pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `max*Length`, `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout`).
        *   Beyond direct textual matches, semantically analyze sections discussing:
            *   Distributed lock behavior, especially timeout and retry logic.
            *   The sequence of operations for acquiring a lock (emphasizing `LockSemaphoreHolder` registration first).
            *   Idempotency, `requestUuid`, and `responseCacheTtl`.
            *   Virtual Thread adoption and MDC propagation.
            *   Watchdog behavior, `interval`, `factor`, `safetyBuffer`, `expiresAtMillis`, `originalLeaseTimeMillis`, and how it never overwrites `originalLeaseTimeMillis`.
            *   Redis key schema, lock-type segments, and semantic isolation.
            *   Lua script return value conventions and Lua-only Redis operations.
            *   Configuration examples, performance tuning, troubleshooting guides.
        *   Remove incorrect/outdated references; update content to accurately reflect the current, refined design.
        *   Ensure consistency with module-specific docs (`configuration.md`, `lock_acquisition.md`, `watchdog.md`, `redis_key_schema.md`).
    3.  **Update Diagrams**: Ensure all Mermaid diagrams (especially in `lock_acquisition.md`) are syntactically correct and reflect the new flows (VT, `LockSemaphoreHolder` order, `acquireTimeout` nuances).
    4.  **Update Glossary**: Expand `glossary.md` to include all new or clarified terms: `lockKey`, `lockDataKey`, `ownerId`, `requestUuid`, `relativeLeaseTimeMillis`, `originalLeaseTimeMillis`, `expiresAtMillis`, `safetyBufferMillis`, `Idempotency Wrapper`, `Lock-Type Segment`.

### Step 9.10: Comprehensive Test Updates

*   **Rationale**: Ensure continued test coverage, remove obsolete tests, and confirm that the new logic behaves as expected.
*   **Affected Files**: `framework-modules/locking/modules/locking-redis-lock/src/test/...`
*   **Actions**:
    1.  Remove/refactor tests for removed properties.
    2.  Ensure existing tests for `acquireTimeout` and indefinite `lock()` still pass.
    3.  Add/enhance tests for:
        *   Idempotency mechanism (simulating lost responses and retries).
        *   Lock-type specific key isolation (negative tests for cross-type operations).
        *   Structured Lua script return value interpretation.
        *   Virtual Thread execution, MDC propagation, and non-pinning behavior.
        *   Watchdog behavior: correct eligibility, `safetyBuffer` calculation, `originalLeaseTimeMillis` preservation, `PEXPIREAT` usage, and `final leg` logic.
    4.  **Test Coverage Metrics**:
        *   Line coverage: ≥85% per implementation class
        *   Branch coverage: ≥80% for conditional logic
        *   Mutation testing score: ≥75% via PIT testing
        *   Integration test coverage: 100% of public API methods
    5.  **Performance Failure Scenarios**:
        *   Redis failover conditions: ≤15ms recovery time at 95th percentile
        *   Network partition scenarios: Graceful degradation with <5% error rate
        *   GC pause impact: Watchdog resilience during 100ms+ GC events
        *   High contention: 1000 concurrent lock attempts with <20% timeout rate
        *   `LockSemaphoreHolder` registration order (race condition prevention).
        *   `acquireTimeout` precedence rules (non-interruption of in-flight Redis ops).
        *   Robust exception handling and propagation.

---

## 10. Rollback Plan

In case of critical issues or if the changes introduce unforeseen instability, the following rollback strategy will be employed:

1.  Revert all code changes to the `locking-redis-lock` module to the state prior to this implementation.
2.  Revert all documentation changes to their prior state.
3.  Re-introduce `pubSubWaitTimeout` and other removed properties if necessary for backward compatibility or to restore previous behavior.
4.  Conduct full regression testing to ensure system stability.
