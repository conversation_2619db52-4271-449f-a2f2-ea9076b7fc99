package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception indicating a permanent error that should not be retried.
 * <p>
 * This exception is used to classify errors that are permanent and will
 * not succeed if retried. Examples include invalid arguments, authentication
 * failures, script compilation errors, or configuration issues.
 * </p>
 * <p>
 * When this exception is thrown, the RedisLockOperationsImpl will not
 * attempt to retry the operation and will propagate the error immediately.
 * </p>
 */
public class NonRetryableLockException extends AbstractRedisLockException {

    private final String failureReason;
    private final String failureCategory;

    /**
     * Constructs a new NonRetryableLockException with the specified details.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be null)
     * @param failureReason   The specific reason why this operation failed
     * @param failureCategory The category of failure (e.g., "validation", "authentication", "configuration")
     * @param message         Descriptive error message
     */
    public NonRetryableLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String failureReason, String failureCategory, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.failureReason = failureReason;
        this.failureCategory = failureCategory;
    }

    /**
     * Constructs a new NonRetryableLockException with the specified details and cause.
     *
     * @param lockName        The full Redis key of the lock involved
     * @param lockType        The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId     The ID of the owner attempting the operation (can be null)
     * @param requestUuid     The unique ID for the lock operation attempt (can be null)
     * @param failureReason   The specific reason why this operation failed
     * @param failureCategory The category of failure (e.g., "validation", "authentication", "configuration")
     * @param message         Descriptive error message
     * @param cause           The underlying cause of this exception
     */
    public NonRetryableLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String failureReason, String failureCategory, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.failureReason = failureReason;
        this.failureCategory = failureCategory;
    }

    /**
     * Gets the specific reason why this operation failed.
     *
     * @return The failure reason
     */
    public String getFailureReason() {
        return failureReason;
    }

    /**
     * Gets the category of failure.
     *
     * @return The failure category
     */
    public String getFailureCategory() {
        return failureCategory;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.failure.reason", failureReason);
        contextMap.put("lock.failure.category", failureCategory);
    }
}
