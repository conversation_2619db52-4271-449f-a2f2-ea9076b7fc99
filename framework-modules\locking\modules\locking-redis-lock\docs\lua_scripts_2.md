# Redis Locking Module: <PERSON><PERSON> Scripts Documentation

## 1. Introduction

Lua scripts provide atomic execution of complex lock operations on the Redis server, ensuring consistency and preventing race conditions. All scripts are loaded by `<PERSON><PERSON><PERSON><PERSON>oa<PERSON>` and executed exclusively by `RedisLockOperationsImpl` via `ClusterCommandExecutor`. The design emphasizes **mandatory idempotency**, **precise timing**, and **lock-type semantic isolation**.

### 1.1 Core Requirements

* **Mandatory Idempotency Wrapper**: All mutating scripts MUST implement the idempotency wrapper pattern
* **Precise Expiry Management**: All scripts use `redis.call('TIME')` for server-side timing and `PEXPIREAT` for absolute expiry
* **Lock-Type Isolation**: All keys include mandatory lock-type segments for semantic isolation
* **Structured Returns**: Standardized return value conventions across all scripts

### 1.2 Redis Cluster Compatibility & Key Format

All keys use hash tags (`{<lockName>}`) for Redis Cluster co-location and MUST include lock-type segments:

**Primary Key Format**: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`
**Data Key Format**: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}:data`
**Response Cache Format**: `<prefix>:<bucketName>:__resp_cache__:<lockType>:{<lockName>}:<requestUuid>`

Lock-type segments include: `reentrant`, `stamped`, `state`, `readwrite`

### 1.3 Standardized Return Value Convention

All lock acquisition and status scripts return a structured Redis array:

1. **`status_code`** (integer):
   - `>= 0`: Lock held by another owner (value represents remaining TTL or expiry info)
   - `< 0`: Lock acquired/operation successful (absolute value may represent TTL or success indicator)

2. **`expiresAtMillis`** (integer): Absolute Unix timestamp (milliseconds) of current lock expiry, calculated using Redis `TIME`

3. **`originalLeaseTimeMillis`** (integer): User's intended relative lease duration, preserved from `lockDataKey`

**Idempotency Wrapper (Mandatory for Mutating Scripts)**:
```lua
-- KEYS[1]: responseCacheKey (e.g., prefix:bucket:__resp_cache__:<lockType>:{lockName}:requestUuid)
-- ARGV[1]: requestUuid
-- ARGV[2]: responseCacheTtl (in ms)
-- ... other KEYS and ARGV for the specific script

local cached_response = redis.call('GET', KEYS[1]);
if cached_response then
    return cjson.decode(cached_response); -- Assuming JSON encoded array
end

-- ... core script logic ...
local result = {status_code, expiresAtMillis, originalLeaseTimeMillis}; -- or other relevant structure

if core_operation_was_successful then -- only cache successes for some ops
    redis.call('PSETEX', KEYS[1], ARGV[2], cjson.encode(result));
end
return result;
```
(Note: `cjson` for encoding/decoding arrays in cache. Simpler values might not need it.)

## 2. Core Lock Operations Scripts

### 2.1. `acquire_lock.lua` (Generic for Reentrant/Simple Exclusive Locks)

*   **Purpose**: Atomically attempts to acquire/re-acquire a lock. Manages `lockKey` and `lockDataKey`.
*   **KEYS**:
    1.  `lockKey`: Main lock key (e.g., `prefix:bucket:__locks__:<lockType>:{myLock}`).
    2.  `lockDataKey`: Metadata key (e.g., `prefix:bucket:__locks__:<lockType>:{myLock}:data`).
    3.  `responseCacheKey`: For idempotency.
*   **ARGV**:
    1.  `requestUuid`: For idempotency.
    2.  `responseCacheTtlMs`: TTL for response cache.
    3.  `relativeLeaseTimeMs`: User's desired lease duration.
    4.  `lockOwnerId`: Client attempting to acquire.
    5.  `currentTimeMsClient`: Client's current time (for reference, server time is preferred).
*   **Logic**:
    1.  Idempotency check using `KEYS[3]`, `ARGV[1]`, `ARGV[2]`.
    2.  Get Redis current time: `local redis_time_parts = redis.call('TIME')`, `local currentTimeRedisMs = (redis_time_parts[1] * 1000) + math.floor(redis_time_parts[2] / 1000)`.
    3.  Calculate `newExpiresAtMillis = currentTimeRedisMs + tonumber(ARGV[3])`.
    4.  Check `lockKey`:
        *   If not exist:
            *   `HSET lockDataKey ownerId ARGV[4] originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
            *   `SET lockKey ARGV[4]`.
            *   `PEXPIREAT lockKey newExpiresAtMillis`.
            *   `PEXPIREAT lockDataKey newExpiresAtMillis + some_buffer_for_data_key (e.g., 60000ms)`.
            *   Result: `{-1, newExpiresAtMillis, tonumber(ARGV[3])}` (acquired).
        *   If exists (is a HASH for reentrant, or STRING for simple):
            *   Get `currentOwnerId` from `lockKey` (or `lockDataKey.ownerId`).
            *   If `currentOwnerId == ARGV[4]` (re-entrant):
                *   Increment re-entry count (if reentrant type, stored in `lockKey` Hash).
                *   `HSET lockDataKey originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
                *   `PEXPIREAT lockKey newExpiresAtMillis`.
                *   `PEXPIREAT lockDataKey newExpiresAtMillis + buffer`.
                *   Result: `{-2, newExpiresAtMillis, tonumber(ARGV[3])}` (re-acquired).
            *   Else (held by other):
                *   Get `currentExpiresAt` from `lockDataKey.expiresAtMillis`.
                *   Result: `{currentTimeRedisMs < currentExpiresAt and (currentExpiresAt - currentTimeRedisMs) or 0, currentExpiresAt, tonumber(redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis'))}`.
    5.  Cache and return `result`.
*   **Returns**: Array `{status_code, expiresAtMillis, originalLeaseTimeMillis}`.

### 2.2. `unlock_lock.lua`

*   **Purpose**: Atomically releases a lock. Publishes unlock message.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `unlockChannelBaseName`: Base for Pub/Sub (e.g., `prefix:bucket:__unlock_channels__:<lockType>`). Script appends `:{lockName}`.
    4.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `lockOwnerId`.
    4.  `unlockTypeMessage`: String payload for Pub/Sub (e.g., "REENTRANT_FULLY_RELEASED").
*   **Logic**:
    1.  Idempotency check.
    2.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    3.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[3]`, result: `{0, 0, 0}` (not released or not owner).
    4.  Else (owner matches):
        *   (If reentrant: decrement count in `lockKey` Hash. If count > 0, result: `{0, current_expires_at, current_original_lease}` (still held), cache & return).
        *   `DEL lockKey lockDataKey`.
        *   Extract `{lockName}` from `KEYS[1]`. Publish `ARGV[4]` to `KEYS[3] .. ':' .. lockNamePart`.
        *   Result: `{1, 0, 0}` (released).
    5.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for released, 0 otherwise), 0, 0}`.

### 2.3. `extend_lock.lua` (User-initiated lease extension)

*   **Purpose**: Atomically extends lease if caller is owner. Updates `originalLeaseTimeMillis`.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `newRelativeLeaseTimeMs`: User's new desired lease from now.
    4.  `lockOwnerId`.
*   **Logic**:
    1.  Idempotency check.
    2.  Get Redis time `currentTimeRedisMs`.
    3.  Calculate `newExpiresAtMillis = currentTimeRedisMs + tonumber(ARGV[3])`.
    4.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    5.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[4]`, result: `{0, current_expires_at_or_0, current_original_lease_or_0}` (not owner/not exist).
    6.  Else (owner matches):
        *   `HSET lockDataKey originalLeaseTimeMillis ARGV[3] expiresAtMillis newExpiresAtMillis`.
        *   `PEXPIREAT lockKey newExpiresAtMillis`.
        *   `PEXPIREAT lockDataKey newExpiresAtMillis + buffer`.
        *   Result: `{1, newExpiresAtMillis, tonumber(ARGV[3])}` (extended).
    7.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for extended), expiresAtMillis, newOriginalLeaseTimeMillis}`.

### 2.4. `watchdog_refresh_lock.lua`

*   **Purpose**: Atomically extends lease if caller is owner, for watchdog. **Does NOT change `originalLeaseTimeMillis`**.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
    3.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `targetAbsoluteExpiresAtMillis`: The new absolute expiry time calculated by watchdog.
    4.  `lockOwnerId`.
*   **Logic**:
    1.  Idempotency check.
    2.  Get `ownerIdFromData = redis.call('HGET', KEYS[2], 'ownerId')`.
    3.  Get `originalLease = redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis')`.
    4.  If `lockKey` not exist or `ownerIdFromData ~= ARGV[4]`, result: `{0, current_expires_at_or_0, originalLease_or_0}`.
    5.  Else (owner matches):
        *   `HSET lockDataKey expiresAtMillis ARGV[3]`. (Note: `originalLeaseTimeMillis` is NOT changed).
        *   `PEXPIREAT lockKey tonumber(ARGV[3])`.
        *   `PEXPIREAT lockDataKey tonumber(ARGV[3]) + buffer`.
        *   Result: `{1, tonumber(ARGV[3]), tonumber(originalLease)}` (refreshed).
    6.  Cache and return `result`.
*   **Returns**: Array `{status_code (1 for refreshed), newExpiresAtMillis, originalLeaseTimeMillis}`.

### 2.5. `check_lock_status.lua`

*   **Purpose**: Atomically gets lock status, owner, `expiresAtMillis`, `originalLeaseTimeMillis`. Non-mutating.
*   **KEYS**:
    1.  `lockKey`.
    2.  `lockDataKey`.
*   **ARGV**: (None beyond potential `requestUuid` if it were to be cached, but reads usually aren't)
*   **Logic**:
    1.  If `lockKey` not exist, result: `{0, 0, 0}` (not held).
    2.  Else:
        *   `owner = redis.call('HGET', KEYS[2], 'ownerId')`.
        *   `expiresAt = tonumber(redis.call('HGET', KEYS[2], 'expiresAtMillis'))`.
        *   `originalLease = tonumber(redis.call('HGET', KEYS[2], 'originalLeaseTimeMillis'))`.
        *   `pttl = redis.call('PTTL', KEYS[1])`.
        *   Result: `{pttl, expiresAt, originalLease}`. (Also include owner in a more complex return if needed by client).
*   **Returns**: Array `{current_pttl_or_0, expiresAtMillis, originalLeaseTimeMillis}`.

## 3. StateLock Specific Scripts

(Similar structure to above, but also interact with a separate state key, ensuring atomicity between lock state and application state. All will use lock-type specific keys and include idempotency wrappers.)

*   `try_state_lock.lua`: Acquires lock if state matches.
*   `update_state_if_locked.lua`: Updates state only if caller holds the lock.
*   `unlock_state_lock.lua`: Releases lock, optionally updates state.

## 4. ReadWriteLock Scripts

These scripts manage shared read and exclusive write access using `lockKey` (type `readwrite`) and `lockDataKey`. They also manage `Individual Read Lock Timeout Key`s.

### 4.1. `try_read_lock.lua` (Conceptual, adapted from `detailed-plan.md` principles)

*   **Purpose**: Acquire read lock. Manages main R/W lock state and individual read lock TTLs.
*   **KEYS**:
    1.  `mainLockKey` (type `readwrite`).
    2.  `mainLockDataKey` (type `readwrite`).
    3.  `individualReadLockTimeoutKeyPrefix`: Base for this reader's timeout keys (e.g., `prefix:bucket:__rwttl__:readwrite:{lockName}:<readerId>`). Script appends `:<reentrantCount>`.
    4.  `responseCacheKey`.
*   **ARGV**:
    1.  `requestUuid`.
    2.  `responseCacheTtlMs`.
    3.  `relativeLeaseTimeMs` (for this specific read acquisition).
    4.  `readerId`.
    5.  `writerId` (current thread's potential write lock ID for reentrant read-while-write).
*   **Logic**:
    1.  Idempotency. Get Redis time.
    2.  Check `mainLockKey`'s mode from its Hash.
    3.  If no lock or mode is 'read', or mode is 'write' but held by `ARGV[5]`:
        *   Increment reader count for `ARGV[4]` in `mainLockKey` Hash.
        *   Create/update `individualReadLockTimeoutKey` (`KEYS[3] .. ':' .. new_reentrant_count`) with `PEXPIREAT (currentTime + ARGV[3])`.
        *   Update `mainLockDataKey`'s `expiresAtMillis` to `max(current_main_expiry, individual_read_expiry)`. Also update `PEXPIREAT` on `mainLockKey`.
        *   Store/update `originalLeaseTimeMillis` in `mainLockDataKey` if this is the first acquirer or policy dictates.
        *   Return success status, new main lock `expiresAtMillis`, and `originalLeaseTimeMillis`.
    4.  Else (conflicting write lock): Return current main lock PTTL/expiry.
*   **Returns**: Standard structured array.

### 4.2. `unlock_read_lock.lua`

*   **Purpose**: Release a read lock instance. Recalculate main R/W lock TTL.
*   **KEYS**:
    1.  `mainLockKey`.
    2.  `mainLockDataKey`.
    3.  `individualReadLockTimeoutKeyPrefixForThisReader`.
    4.  `generalIndividualReadLockTimeoutKeyScanPattern` (e.g., `prefix:bucket:__rwttl__:readwrite:{lockName}:*`).
    5.  `unlockChannelBaseName`.
    6.  `responseCacheKey`.
*   **ARGV**: `requestUuid`, `responseCacheTtlMs`, `readerId`, `unlockTypeMessage`.
*   **Logic**:
    1.  Idempotency.
    2.  Decrement reader count for `ARGV[3]` in `mainLockKey` Hash. Delete corresponding `individualReadLockTimeoutKey`.
    3.  If all readers and writer are gone (check `mainLockKey` Hash): `DEL mainLockKey, mainLockDataKey`. Publish. Return success.
    4.  Else: Iterate all remaining `individualReadLockTimeoutKey`s (using `SCAN` with `KEYS[4]`) to find max remaining TTL.
    5.  Update `mainLockDataKey.expiresAtMillis` and `PEXPIREAT mainLockKey` to this max TTL (or its own watchdog-managed TTL if greater).
    6.  Publish. Return success.
*   **Returns**: Standard structured array.

### 4.3. `try_write_lock.lua` & 4.4. `unlock_write_lock.lua`

*   Similar principles: manage `mainLockKey` (mode='write', writerId, reentrancy) and `mainLockDataKey`.
*   `unlock_write_lock` checks for active readers. If readers exist, may transition `mainLockKey` mode to 'read' and set its TTL based on max remaining reader TTLs. Otherwise, deletes the lock. Publishes appropriate `UnlockType`.

## 5. StampedLock Scripts

(Conceptual, follow principles of `lockKey` with `stamped` type, `lockDataKey`, idempotency, structured returns, `PEXPIREAT`.)

*   `try_stamped_lock.lua` (modes: "read", "write", "optimistic")
*   `unlock_stamped_lock.lua`
*   `validate_stamp.lua`
*   `convert_to_write_lock.lua`
*   `convert_to_read_lock.lua`