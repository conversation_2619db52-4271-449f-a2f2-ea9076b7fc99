# Redis Locking Module: Class Hierarchy

## 1. Introduction

This document outlines the comprehensive class hierarchy for the `locking-redis-lock` module. The hierarchy supports all documented lock types, adhering to the **Asynchronous-First approach with Virtual Threads** and aligning with standard Java patterns and Destilink Framework guidelines. The design emphasizes centralized idempotency management, lock-type semantic isolation, and robust Virtual Thread integration.

## 2. Core Design Principles

* **Asynchronous-First Operations**: All core lock operations (`lockAsync`, `tryLockAsync`, `unlockAsync`) are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads.
* **Virtual Thread Integration**: All lock operations are executed on Virtual Threads with immediate handoff from platform threads and proper MDC propagation.
* **Standard Java Compliance**: Integrates seamlessly with `java.util.concurrent.locks.Lock` and `ReadWriteLock` interfaces.
* **Centralized Idempotency**: `RedisLockOperationsImpl` serves as the single point for `requestUuid` generation and idempotency management across all lock operations.
* **Lock-Type Semantic Isolation**: All Redis keys include mandatory lock-type segments for semantic isolation and prevention of cross-type interference.
* **Configuration Hierarchy**: Behavior influenced by `RedisLockProperties` with clear override precedence (instance-specific > bucket-level > global defaults).
* **Watchdog Integration**: Conditional monitoring based on `safetyBufferMillis` calculations and `LockOwnerSupplier.canUseWatchdog()` validation.
* **Lua-Only Redis Operations**: All lock state, TTL, and metadata changes exclusively via Lua scripts for atomicity and consistency.

## 3. Class Hierarchy Diagram

```mermaid
classDiagram
    direction TB

    class Lock {
        <<interface>>
        +lock()
        +tryLock()
        +unlock()
        +newCondition()
    }

    class ReadWriteLock {
        <<interface>>
        +readLock() Lock
        +writeLock() Lock
    }

    class AsyncLock {
        <<interface>>
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +tryLockAsync(long, TimeUnit) CompletableFuture
        +unlockAsync() CompletableFuture
    }
    AsyncLock --|> Lock

    class AsyncReadWriteLock {
        <<interface>>
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    AsyncReadWriteLock --|> ReadWriteLock

    class AbstractRedisLock {
        <<Abstract>>
        #String lockKeyWithTypeSegment
        #String ownerId
        #RedisLockOperations redisLockOperations
        #RedisLockProperties redisLockProperties
        #LockOwnerSupplier lockOwnerSupplier
        #LockSemaphoreHolder lockSemaphoreHolder
        #ExecutorService virtualThreadExecutor
        #LockComponentRegistry componentRegistry
        +lockAsync() CompletableFuture
        +tryLockAsync() CompletableFuture
        +unlockAsync() CompletableFuture
        +extendLeaseAsync() CompletableFuture
        +lock() // blocking wrapper via Virtual Thread
        +tryLock() // blocking wrapper via Virtual Thread
        +unlock() // blocking wrapper via Virtual Thread
        #<i>Immediate handoff to Virtual Thread executor</i>
        #<i>MDC context capture and propagation</i>
        #<i>Registers LockSemaphoreHolder BEFORE first Redis attempt</i>
        #<i>No requestUuid field - managed by RedisLockOperationsImpl</i>
    }
    AbstractRedisLock ..|> AsyncLock

    class RedisReentrantLock {
        // Uses 'reentrant' lock-type segment in keys
    }
    RedisReentrantLock --|> AbstractRedisLock

    class RedisStateLock {
        // Uses 'state' lock-type segment in keys
    }
    RedisStateLock --|> AbstractRedisLock

    class RedisStampedLock {
        // Uses 'stamped' lock-type segment in keys
    }
    RedisStampedLock --|> AbstractRedisLock

    class RedisReadWriteLock {
        // Uses 'readwrite' lock-type segment in keys
        -AsyncLock readLockInstance
        -AsyncLock writeLockInstance
        +readLock() AsyncLock
        +writeLock() AsyncLock
    }
    RedisReadWriteLock ..|> AsyncReadWriteLock

    class RedisReadLock {
        // Part of RedisReadWriteLock
    }
    RedisReadLock --|> AbstractRedisLock

    class RedisWriteLock {
        // Part of RedisReadWriteLock
    }
    RedisWriteLock --|> AbstractRedisLock

    RedisReadWriteLock *-- RedisReadLock : creates
    RedisReadWriteLock *-- RedisWriteLock : creates
```

## 4. Interface and Class Descriptions

### 4.1. Standard Java Lock Interfaces

* **`java.util.concurrent.locks.Lock`**: Standard Java lock interface providing synchronous lock operations.
* **`java.util.concurrent.locks.ReadWriteLock`**: Standard Java interface for read-write lock pairs.

### 4.2. Custom Asynchronous Lock Interfaces

* **`com.tui.destilink.framework.locking.redis.lock.AsyncLock`**
  * **Extends:** `java.util.concurrent.locks.Lock`
  * **Purpose:** Defines contract for asynchronous lock operations executed on Virtual Threads. All async methods return `CompletableFuture`s.
  * **Key Methods:** `lockAsync()`, `tryLockAsync()`, `tryLockAsync(long, TimeUnit)`, `unlockAsync()`, `extendLeaseAsync()`

* **`com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock`**
  * **Extends:** `java.util.concurrent.locks.ReadWriteLock`
  * **Purpose:** Asynchronous read-write lock interface returning `AsyncLock` instances.

### 4.3. Abstract Base Class

* **`com.tui.destilink.framework.locking.redis.lock.AbstractRedisLock`**
  * **Implements:** `com.tui.destilink.framework.locking.redis.lock.AsyncLock`
  * **Key Responsibilities:**
    * **Virtual Thread Execution**: Implements immediate handoff pattern - all public methods dispatch work to Virtual Thread executor
    * **MDC Propagation**: Captures and restores MDC context in Virtual Threads for consistent logging
    * **Lock Key Management**: Constructs Redis keys with mandatory lock-type segments for semantic isolation
    * **Component Access**: Uses `LockComponentRegistry` to access shared services (no direct field injection)
    * **Idempotency Delegation**: Delegates all `requestUuid` generation and idempotency to `RedisLockOperationsImpl`
    * **Semaphore Registration**: Ensures `LockSemaphoreHolder` is registered **before** first Redis attempt to prevent race conditions
    * **Watchdog Integration**: Manages conditional watchdog registration based on `safetyBufferMillis` and `canUseWatchdog()`
    * **Blocking Wrappers**: Synchronous `Lock` methods block on `CompletableFuture.get()` while Virtual Thread executes
    * **No requestUuid Field**: Removed `requestUuid` field - now managed centrally by `RedisLockOperationsImpl`

### 4.4. Concrete Redis Lock Implementations

All implementations reside in `com.tui.destilink.framework.locking.redis.lock.impl` and extend `AbstractRedisLock`. Each uses a specific lock-type segment in Redis keys for semantic isolation:

* **`RedisReentrantLock`**: Primary reentrant distributed lock. Uses `reentrant` lock-type segment.
* **`RedisStateLock`**: Specialized lock with associated state management. Uses `state` lock-type segment.
* **`RedisStampedLock`**: Distributed stamped lock with optimistic read capabilities. Uses `stamped` lock-type segment.
* **`RedisReadWriteLock`**: Composite lock containing separate read and write locks. Uses `readwrite` lock-type segment.
* **`RedisReadLock` / `RedisWriteLock`**: Individual components of `RedisReadWriteLock`, each extending `AbstractRedisLock`.

### 4.5. Supporting Classes

* **`RedisLockOperationsImpl`**: Central component responsible for:
  * Generating unique `requestUuid` per logical operation
  * Managing idempotency across all mutating Lua scripts
  * Handling internal retries for individual Redis operations
  * Executing Lua scripts via `ClusterCommandExecutor`

* **`LockSemaphoreHolder`**: Manages `CompletableFuture`-based waiting for lock acquisition with Pub/Sub integration.

* **`RedisLockProperties`**: Configuration class with nested `WatchdogProperties` and `Defaults` for comprehensive configuration management.

## 5. Watchdog-Eligible Lock Behavior

This is a behavioral characteristic applied to locks that meet specific criteria, not a distinct class type. A lock becomes eligible for watchdog monitoring when:

1. `LockOwnerSupplier.canUseWatchdog()` returns `true` (indicating the lock is owned by the current application instance)
2. The lock's `userProvidedLeaseTime` (relative lease time in milliseconds) is greater than `safetyBufferMillis` (calculated as `watchdog.interval * watchdog.factor`)

When both conditions are met:

* `AbstractRedisLock` registers the lock with the always-active `LockWatchdog` service
* The watchdog manages lease extension using `PEXPIREAT` with Redis server time precision
* The `originalLeaseTimeMillis` stored in Redis is preserved and never modified by the watchdog
* Initial lock TTL is set to `safetyBufferMillis` instead of the full user lease time
* Watchdog extends the lock periodically until the user's intended expiry time is reached

## 6. Key Architectural Changes

### 6.1 Removed Components

* **`requestUuid` field**: Removed from `AbstractRedisLock` - now managed centrally by `RedisLockOperationsImpl`
* **Direct Redis operations**: All lock state changes now exclusively via Lua scripts

### 6.2 Enhanced Components

* **Virtual Thread Integration**: Complete adoption with immediate handoff and MDC propagation
* **Centralized Idempotency**: All `requestUuid` generation and response caching handled by `RedisLockOperationsImpl`
* **Lock-Type Isolation**: Mandatory lock-type segments in all Redis keys prevent cross-type interference
* **Always-Active Watchdog**: Service runs continuously with conditional per-lock monitoring