-- Redis Check Lock Status Script
-- This script checks the current status of a lock and returns structured information
-- 
-- KEYS[1] - The main lock key (format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>})
-- KEYS[2] - The lock data key (Redis Hash for metadata)
-- 
-- ARGV[1] - Lock type (e.g., "reentrant", "stamped", "state")

local lockKey = KEYS[1]
local lockDataKey = KEYS[2]
local lockType = ARGV[1]

-- ========================================
-- CORE CHECK LOGIC
-- ========================================
-- Get current Redis server time for precise calculations
local timeResult = redis.call('TIME')
local currentTimeMillis = tonumber(timeResult[1]) * 1000 + math.floor(tonumber(timeResult[2]) / 1000)

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local statusCode
local expiresAtMillis = 0
local originalLeaseTimeMillis = 0

if currentOwner == false then
    -- Lock doesn't exist
    statusCode = 0  -- Not locked
    
else
    -- Lock exists, get TTL and metadata
    local currentTtl = redis.call('PTTL', lockKey)
    
    if currentTtl == -1 then
        -- Lock exists but no TTL (forever)
        statusCode = -1  -- Locked forever
        expiresAtMillis = 0  -- No expiration
    elseif currentTtl == -2 then
        -- Key doesn't exist (race condition)
        statusCode = 0  -- Not locked
    else
        -- Lock exists with TTL
        statusCode = 1  -- Locked with TTL
        expiresAtMillis = currentTimeMillis + currentTtl
    end
    
    -- Get metadata if available
    local storedExpiresAt = redis.call('HGET', lockDataKey, 'expiresAtMillis')
    local storedOriginalLeaseTime = redis.call('HGET', lockDataKey, 'originalLeaseTimeMillis')
    
    if storedExpiresAt then
        expiresAtMillis = tonumber(storedExpiresAt)
    end
    
    if storedOriginalLeaseTime then
        originalLeaseTimeMillis = tonumber(storedOriginalLeaseTime)
    end
end

-- ========================================
-- PREPARE STRUCTURED RESPONSE
-- ========================================
local response = {
    statusCode,
    expiresAtMillis,
    originalLeaseTimeMillis,
    currentOwner or ""  -- Return owner ID or empty string
}

return response
