# Redis Locking Module: Exception Handling Strategy

## 1. Introduction

Robust error handling is crucial for distributed systems. The `locking-redis-lock` module employs a comprehensive structured exception hierarchy that provides clear, actionable error information while integrating seamlessly with the Destilink Framework's structured logging via `ExceptionMarkerProvider`. This strategy is designed for the **Asynchronous-First approach with Virtual Threads**, exclusive `redis-core` interaction, and centralized idempotency management.

The exception handling system ensures that all errors are properly categorized, contextual information is preserved, and MDC context is propagated correctly through Virtual Thread execution. `RedisLockErrorHandler` serves as the central translation layer for low-level Redis exceptions from `ClusterCommandExecutor`, while `RedisLockOperationsImpl` manages retry logic for transient failures.

## 2. Base Exception: `AbstractRedisLockException`

All custom lock-related exceptions extend `AbstractRedisLockException`, providing a well-defined exception hierarchy with clear, actionable error information.

### 2.1 Core Characteristics

* **Common Base Type**: Enables consistent exception handling across all lock operations
* **Contextual Information**: Carries comprehensive context for debugging and monitoring
* **Framework Integration**: Seamlessly integrates with `ExceptionMarkerProvider` for structured logging
* **Virtual Thread Compatibility**: Designed to work correctly with Virtual Thread execution model

### 2.2 Required Contextual Fields

* **`lockName`**: Full Redis key including lock-type segment (e.g., `myApp:orders:__locks__:reentrant:{order123}`)
* **`lockType`**: Lock type identifier for semantic isolation (e.g., `reentrant`, `stamped`, `state`)
* **`lockOwnerId`**: Unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`)
* **`requestUuid`**: Unique identifier for the logical operation (managed by `RedisLockOperationsImpl`)
* **`message`**: Human-readable error description
* **`cause`**: Underlying exception that caused this error (if applicable)

### 2.3 ExceptionMarkerProvider Integration

The `ExceptionMarkerProvider` implementation populates SLF4J `Marker` with both common and exception-specific fields, ensuring structured JSON log output that integrates with the Destilink Framework's logging infrastructure.

## 3. Specialized Exception Classes

All specialized exceptions extend `AbstractRedisLockException` and are designed to propagate correctly through `CompletableFuture`s from Virtual Thread execution.

### 3.1 Lock Operation Exceptions

* **`LockAcquisitionException`**: General failure during lock acquisition (excluding timeout/interruption scenarios)
* **`LockAcquisitionTimeoutException`**:
  * **Purpose**: Lock acquisition failed within the specified `acquireTimeout`
  * **Specific Context**: `timeoutMillis`, `attemptCount`
  * **Critical Note**: Only raised if `acquireTimeout` expires AND the latest Redis operation has completed without acquiring the lock AND without throwing a Redis-specific exception
* **`LockReleaseException`**: Failure during lock release operations
* **`LockInterruptedException`**: Wraps `InterruptedException` during synchronous lock wait operations

### 3.2 Lease Management Exceptions

* **`LeaseExtensionException`**: Failure during watchdog or user-initiated lease extension
* **`LockExtensionException`**: General lock extension failures

### 3.3 Redis Operation Exceptions

* **`LockCommandException`**: Lua script or Redis command execution failures from `ClusterCommandExecutor`
* **`LockConnectionException`**: Underlying Redis connection problems from `redis-core` module

### 3.4 Lock State Exceptions

* **`LockNotOwnedException`**: Attempt to operate on a lock not held by the caller
* **`LockIllegalMonitorStateException`**: Invalid lock state for the requested operation

### 3.5 Retry Classification Exceptions

* **`RetryableLockException`**: Indicates a transient error that `RedisLockOperationsImpl` can retry
  * Examples: Temporary network issues, Redis server busy, connection timeouts
  * Triggers internal retry logic using `maxRetries` and `retryInterval`
* **`NonRetryableLockException`**: Indicates a permanent error that should not be retried
  * Examples: Invalid arguments, authentication failures, script compilation errors
  * Propagated immediately without retry attempts

### 3.6 Idempotency-Related Exceptions

* **`IdempotencyViolationException`**: Idempotency system detects inconsistent state
  * **Context**: `requestUuid`, `cacheKey`, `expectedResponse`, `actualResponse`
  * **Cause**: Typically indicates a serious system issue or data corruption
* **`IdempotencyTimeoutException`**: Operations on idempotency response cache timeout
  * **Context**: `requestUuid`, `timeoutMillis`
  * **Classification**: Potentially retryable by `RedisLockOperationsImpl`
* **`ResponseCacheException`**: General failures in response cache system
  * **Context**: `requestUuid`, `operation`, `cacheKey`

## 4. Error Handling by `RedisLockErrorHandler`

The `RedisLockErrorHandler` serves as the central translation layer between low-level Redis exceptions and the module's structured exception hierarchy.

### 4.1 Core Responsibilities

* **Exception Translation**: Catches low-level Redis exceptions from `ClusterCommandExecutor` and translates them into appropriate `AbstractRedisLockException` subtypes
* **Context Enrichment**: Adds full contextual information including `requestUuid`, `lockName`, `lockType`, and `ownerId`
* **CompletableFuture Handling**: Properly unwraps and handles `CompletionException` and `ExecutionException` from Virtual Thread execution
* **MDC Preservation**: Ensures MDC context is maintained during exception processing

### 4.2 Exception Mapping Strategy

* **Connection Issues**: `LockConnectionException` for Redis connectivity problems
* **Command Failures**: `LockCommandException` for Lua script or Redis command execution failures
* **Timeout Issues**: Appropriate timeout exceptions based on the operation context
* **Authentication/Authorization**: `NonRetryableLockException` for security-related failures

## 5. Error Handling by `RedisLockOperationsImpl`

`RedisLockOperationsImpl` serves as the single point of responsibility for managing Redis operation retries and exception classification.

### 5.1 Exception Processing Flow

* **Exception Capture**: Catches all exceptions from `ClusterCommandExecutor` and Lua script executions
* **Classification**: Maps exceptions to either `RetryableLockException` or `NonRetryableLockException`
* **Retry Logic**: Implements internal retry logic for transient failures
* **Context Propagation**: Ensures `requestUuid` and other context is preserved through retries

### 5.2 Retry Behavior

* **Retryable Operations**: For individual Redis operations resulting in `RetryableLockException`
  * Uses `defaults.maxRetries` for maximum retry attempts
  * Uses `defaults.retryInterval` for delay between retries
  * Employs `Thread.sleep()` on Virtual Threads for efficient waiting
  * Maintains the same `requestUuid` across all retry attempts
* **Non-Retryable Operations**: `NonRetryableLockException` types are propagated immediately without retry attempts

## 6. `acquireTimeout` Precedence Rules

The `acquireTimeout` configuration property defines critical precedence rules for exception handling in lock acquisition scenarios.

### 6.1 Core Principles

* **Approximate Limit**: `defaults.acquireTimeout` serves as an approximate overall limit for user-facing `tryLock` attempts
* **Non-Interruption Policy**: If `acquireTimeout` expires while a Redis command is in flight, that command **MUST NOT be interrupted** by the locking module
* **Operation Completion**: In-flight operations complete or timeout based on their own lower-level settings (Lettuce/`ClusterCommandExecutor`)

### 6.2 Result Precedence

The actual result of an in-flight Redis operation takes precedence over timeout considerations:

* **Successful Acquisition**: If the Redis operation successfully acquires the lock, the lock is considered acquired, even if `acquireTimeout` passed during the operation
* **Redis Error**: If the operation fails with a Redis error (after `RedisLockOperationsImpl` exhausts its retries), that specific error is propagated
* **Timeout Exception**: `LockAcquisitionTimeoutException` is only raised when:
  1. `acquireTimeout` deadline has passed, AND
  2. The latest Redis operation has completed without acquiring the lock, AND
  3. The operation did not result in a Redis-specific exception

## 7. Virtual Thread Integration and MDC Propagation

### 7.1 Virtual Thread Exception Handling

* **Exception Propagation**: All exceptions properly propagate through `CompletableFuture` chains from Virtual Thread execution
* **Context Preservation**: Exception context is maintained across Virtual Thread boundaries
* **Non-Blocking Behavior**: Exception handling does not block platform threads

### 7.2 MDC Context Management

* **Context Capture**: MDC context is captured from the calling thread before Virtual Thread dispatch
* **Context Restoration**: MDC context is restored in Virtual Threads during exception processing
* **Context Cleanup**: MDC context is properly cleaned up after exception handling completes

## 8. Logging Integration

### 8.1 Structured Logging

* **ExceptionMarkerProvider**: Ensures structured JSON log output via Destilink Core logging infrastructure
* **Contextual Information**: All exceptions include comprehensive contextual information in log markers
* **Consistent Format**: Standardized log format across all exception types

### 8.2 Logging Best Practices

* **Parameterized Messages**: General logging uses SLF4J with parameterized messages for performance
* **MDC Enrichment**: `LockContextDecorator` enriches logs with MDC data propagated to Virtual Threads
* **Error Correlation**: `requestUuid` enables correlation of errors across distributed operations
* **Observability**: Exception logs integrate with monitoring and alerting systems