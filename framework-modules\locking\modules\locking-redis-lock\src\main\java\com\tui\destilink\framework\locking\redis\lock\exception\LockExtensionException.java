package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock extension operation fails.
 * <p>
 * This exception is thrown when an attempt to extend a lock's lease time
 * fails, either through manual extension or watchdog operations.
 * </p>
 */
public class LockExtensionException extends AbstractRedisLockException {

    private final String extensionType;
    private final long requestedExtensionMillis;

    /**
     * Constructs a new LockExtensionException with the specified details.
     *
     * @param lockName                 The full Redis key of the lock involved
     * @param lockType                 The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId              The ID of the owner attempting the operation (can be null)
     * @param requestUuid              The unique ID for the lock operation attempt (can be null)
     * @param extensionType            The type of extension (e.g., "manual", "watchdog")
     * @param requestedExtensionMillis The requested extension duration in milliseconds
     * @param message                  Descriptive error message
     */
    public LockExtensionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String extensionType, long requestedExtensionMillis, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.extensionType = extensionType;
        this.requestedExtensionMillis = requestedExtensionMillis;
    }

    /**
     * Constructs a new LockExtensionException with the specified details and cause.
     *
     * @param lockName                 The full Redis key of the lock involved
     * @param lockType                 The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId              The ID of the owner attempting the operation (can be null)
     * @param requestUuid              The unique ID for the lock operation attempt (can be null)
     * @param extensionType            The type of extension (e.g., "manual", "watchdog")
     * @param requestedExtensionMillis The requested extension duration in milliseconds
     * @param message                  Descriptive error message
     * @param cause                    The underlying cause of this exception
     */
    public LockExtensionException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String extensionType, long requestedExtensionMillis, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.extensionType = extensionType;
        this.requestedExtensionMillis = requestedExtensionMillis;
    }

    /**
     * Gets the type of extension that failed.
     *
     * @return The type of extension (e.g., "manual", "watchdog")
     */
    public String getExtensionType() {
        return extensionType;
    }

    /**
     * Gets the requested extension duration in milliseconds.
     *
     * @return The requested extension duration in milliseconds
     */
    public long getRequestedExtensionMillis() {
        return requestedExtensionMillis;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.extension.type", extensionType);
        contextMap.put("lock.extension.millis", requestedExtensionMillis);
    }
}
