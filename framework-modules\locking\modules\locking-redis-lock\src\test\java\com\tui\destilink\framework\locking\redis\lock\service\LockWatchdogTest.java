package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.test.TestApplication;
import com.tui.destilink.framework.test.support.core.util.TestUtils;
import com.tui.destilink.framework.test.support.redis.annotation.RedisTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test suite for {@link LockWatchdog} behavior.
 * <p>
 * Tests ensure that:
 * - Watchdog is always active when module is enabled
 * - Conditional monitoring based on safety buffer calculation
 * - Proper lease extension without modifying originalLeaseTimeMillis
 * - Correct handling of lock expiration and unregistration
 * - Integration with RedisLockOperations for retries
 * </p>
 */
@RedisTestSupport(keyspacePrefixes = { "test-watchdog:" })
@SpringBootTest(classes = TestApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class LockWatchdogTest {

    private static final String UNIQUE_ID = TestUtils.generateTestClassId(LockWatchdogTest.class);

    @Autowired
    private LockWatchdog lockWatchdog;

    @Autowired
    private RedisLockOperations redisLockOperations;

    @Autowired
    private LockOwnerSupplier lockOwnerSupplier;

    @Autowired
    private RedisLockProperties redisLockProperties;

    private String lockKey;
    private String ownerId;

    @BeforeEach
    void setUp() {
        lockKey = "test-watchdog:" + UNIQUE_ID + ":lock";
        ownerId = lockOwnerSupplier.get();
    }

    @Test
    @DisplayName("Should be active when module is enabled")
    void shouldBeActiveWhenModuleEnabled() {
        // Watchdog should be instantiated and available
        assertThat(lockWatchdog).isNotNull();
        
        // Should be able to register locks (even if they're not monitored)
        Duration leaseTime = Duration.ofSeconds(10);
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        assertThat(handle).isNotNull();
        
        // Clean up
        handle.unregister();
    }

    @Test
    @DisplayName("Should register and unregister locks correctly")
    void shouldRegisterAndUnregisterLocks() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register lock
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        assertThat(handle).isNotNull();
        
        // Unregister using handle
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
        
        // Multiple unregistrations should be safe
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should unregister locks directly")
    void shouldUnregisterLocksDirectly() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register lock
        lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Unregister directly
        assertThatCode(() -> lockWatchdog.unregisterLock(lockKey, ownerId))
                .doesNotThrowAnyException();
        
        // Multiple unregistrations should be safe
        assertThatCode(() -> lockWatchdog.unregisterLock(lockKey, ownerId))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle lock extension through RedisLockOperations")
    void shouldHandleLockExtensionThroughRedisLockOperations() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // First acquire the lock in Redis
        CompletableFuture<Boolean> lockAcquired = redisLockOperations.tryLock(lockKey, ownerId, leaseTime);
        assertThat(lockAcquired).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Register with watchdog
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Wait a bit to allow potential watchdog activity
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Lock should still be held (watchdog should maintain it)
        CompletableFuture<Boolean> isLocked = redisLockOperations.isLocked(lockKey);
        assertThat(isLocked).succeedsWithin(Duration.ofSeconds(5))
                .isEqualTo(true);
        
        // Clean up
        handle.unregister();
        redisLockOperations.unlock(lockKey, ownerId).join();
    }

    @Test
    @DisplayName("Should handle watchdog configuration properties")
    void shouldHandleWatchdogConfigurationProperties() {
        RedisLockProperties.WatchdogProperties watchdogProps = redisLockProperties.getWatchdog();
        
        // Verify configuration is loaded
        assertThat(watchdogProps).isNotNull();
        assertThat(watchdogProps.getInterval()).isNotNull();
        assertThat(watchdogProps.getFactor()).isGreaterThan(0.0);
        assertThat(watchdogProps.getCorePoolSize()).isGreaterThan(0);
        assertThat(watchdogProps.getThreadNamePrefix()).isNotBlank();
        assertThat(watchdogProps.getShutdownAwaitTermination()).isNotNull();
    }

    @Test
    @DisplayName("Should handle multiple concurrent registrations")
    void shouldHandleMultipleConcurrentRegistrations() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register multiple locks
        String lockKey1 = lockKey + ":1";
        String lockKey2 = lockKey + ":2";
        String lockKey3 = lockKey + ":3";
        
        LockWatchdog.WatchdogHandle handle1 = lockWatchdog.registerLock(lockKey1, ownerId, leaseTime);
        LockWatchdog.WatchdogHandle handle2 = lockWatchdog.registerLock(lockKey2, ownerId, leaseTime);
        LockWatchdog.WatchdogHandle handle3 = lockWatchdog.registerLock(lockKey3, ownerId, leaseTime);
        
        assertThat(handle1).isNotNull();
        assertThat(handle2).isNotNull();
        assertThat(handle3).isNotNull();
        
        // Unregister all
        handle1.unregister();
        handle2.unregister();
        handle3.unregister();
    }

    @Test
    @DisplayName("Should handle shutdown gracefully")
    void shouldHandleShutdownGracefully() {
        Duration leaseTime = Duration.ofSeconds(30);
        
        // Register a lock
        LockWatchdog.WatchdogHandle handle = lockWatchdog.registerLock(lockKey, ownerId, leaseTime);
        
        // Shutdown should not throw exceptions
        assertThatCode(() -> lockWatchdog.shutdown()).doesNotThrowAnyException();
        
        // Clean up (should still work after shutdown)
        assertThatCode(() -> handle.unregister()).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("Should handle different lease durations")
    void shouldHandleDifferentLeaseDurations() {
        // Test with various lease durations
        Duration shortLease = Duration.ofSeconds(5);
        Duration mediumLease = Duration.ofSeconds(30);
        Duration longLease = Duration.ofMinutes(5);
        
        LockWatchdog.WatchdogHandle handle1 = lockWatchdog.registerLock(lockKey + ":short", ownerId, shortLease);
        LockWatchdog.WatchdogHandle handle2 = lockWatchdog.registerLock(lockKey + ":medium", ownerId, mediumLease);
        LockWatchdog.WatchdogHandle handle3 = lockWatchdog.registerLock(lockKey + ":long", ownerId, longLease);
        
        assertThat(handle1).isNotNull();
        assertThat(handle2).isNotNull();
        assertThat(handle3).isNotNull();
        
        // Clean up
        handle1.unregister();
        handle2.unregister();
        handle3.unregister();
    }
}
