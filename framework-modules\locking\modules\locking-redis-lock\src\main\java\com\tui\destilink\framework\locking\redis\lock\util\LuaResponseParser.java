package com.tui.destilink.framework.locking.redis.lock.util;

import lombok.Data;
import lombok.experimental.UtilityClass;

import java.util.List;

/**
 * Utility class for parsing structured responses from Lua scripts.
 * <p>
 * This class implements the standardized return value conventions for Lua scripts
 * as specified in Step 9.6 of the detailed plan. All Lua scripts should return
 * structured responses with consistent format.
 * </p>
 * <p>
 * Standard response format:
 * - Index 0: status_code (Long) - Operation result code
 * - Index 1: expiresAtMillis (Long) - Absolute expiration timestamp
 * - Index 2: originalLeaseTimeMillis (Long) - Original lease duration
 * </p>
 */
@UtilityClass
public class LuaResponseParser {

    /**
     * Parses a structured response from a lock operation Lua script.
     *
     * @param response The response from the Lua script
     * @return Parsed lock operation response
     * @throws IllegalArgumentException if the response format is invalid
     */
    public static LockOperationResponse parseLockResponse(Object response) {
        if (response instanceof Long) {
            // Legacy format - just status code
            return new LockOperationResponse((Long) response, null, null);
        }
        
        if (response instanceof List) {
            List<?> list = (List<?>) response;
            if (list.size() >= 3) {
                Long statusCode = safeCastToLong(list.get(0));
                Long expiresAtMillis = safeCastToLong(list.get(1));
                Long originalLeaseTimeMillis = safeCastToLong(list.get(2));
                
                return new LockOperationResponse(statusCode, expiresAtMillis, originalLeaseTimeMillis);
            } else if (list.size() >= 1) {
                // Partial structured response
                Long statusCode = safeCastToLong(list.get(0));
                return new LockOperationResponse(statusCode, null, null);
            }
        }
        
        throw new IllegalArgumentException("Invalid Lua script response format: " + response);
    }

    /**
     * Parses a structured response from an unlock operation Lua script.
     *
     * @param response The response from the Lua script
     * @return Parsed unlock operation response
     * @throws IllegalArgumentException if the response format is invalid
     */
    public static UnlockOperationResponse parseUnlockResponse(Object response) {
        if (response instanceof Long) {
            // Legacy format - just status code
            return new UnlockOperationResponse((Long) response, null, null);
        }
        
        if (response instanceof List) {
            List<?> list = (List<?>) response;
            if (list.size() >= 3) {
                Long statusCode = safeCastToLong(list.get(0));
                Boolean unlocked = safeCastToBoolean(list.get(1));
                String expirationInfo = safeCastToString(list.get(2));
                
                return new UnlockOperationResponse(statusCode, unlocked, expirationInfo);
            } else if (list.size() >= 1) {
                // Partial structured response
                Long statusCode = safeCastToLong(list.get(0));
                return new UnlockOperationResponse(statusCode, null, null);
            }
        }
        
        throw new IllegalArgumentException("Invalid Lua script response format: " + response);
    }

    /**
     * Safely casts an object to Long, handling various numeric types.
     *
     * @param obj The object to cast
     * @return The Long value, or null if the object is null
     * @throws IllegalArgumentException if the object cannot be cast to Long
     */
    private static Long safeCastToLong(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }
        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }
        throw new IllegalArgumentException("Cannot cast to Long: " + obj.getClass().getSimpleName());
    }

    /**
     * Safely casts an object to Boolean.
     *
     * @param obj The object to cast
     * @return The Boolean value, or null if the object is null
     * @throws IllegalArgumentException if the object cannot be cast to Boolean
     */
    private static Boolean safeCastToBoolean(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        if (obj instanceof Long) {
            return ((Long) obj) != 0;
        }
        if (obj instanceof Integer) {
            return ((Integer) obj) != 0;
        }
        throw new IllegalArgumentException("Cannot cast to Boolean: " + obj.getClass().getSimpleName());
    }

    /**
     * Safely casts an object to String.
     *
     * @param obj The object to cast
     * @return The String value, or null if the object is null
     */
    private static String safeCastToString(Object obj) {
        if (obj == null) {
            return null;
        }
        return obj.toString();
    }

    /**
     * Represents a structured response from a lock operation Lua script.
     */
    @Data
    public static class LockOperationResponse {
        private final Long statusCode;
        private final Long expiresAtMillis;
        private final Long originalLeaseTimeMillis;

        /**
         * Checks if the operation was successful.
         *
         * @return true if the status code indicates success
         */
        public boolean isSuccess() {
            return statusCode != null && statusCode == 1L;
        }

        /**
         * Checks if this response has timing information.
         *
         * @return true if both expiresAtMillis and originalLeaseTimeMillis are present
         */
        public boolean hasTimingInfo() {
            return expiresAtMillis != null && originalLeaseTimeMillis != null;
        }
    }

    /**
     * Represents a structured response from an unlock operation Lua script.
     */
    @Data
    public static class UnlockOperationResponse {
        private final Long statusCode;
        private final Boolean unlocked;
        private final String expirationInfo;

        /**
         * Checks if the operation was successful.
         *
         * @return true if the status code indicates success
         */
        public boolean isSuccess() {
            return statusCode != null && statusCode == 1L;
        }

        /**
         * Checks if the lock was actually unlocked.
         *
         * @return true if the lock was unlocked, false if it wasn't locked by this owner
         */
        public boolean wasUnlocked() {
            return Boolean.TRUE.equals(unlocked);
        }
    }
}
