package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when a lock operation is attempted in an invalid state.
 * <p>
 * This exception is thrown when an operation is attempted on a lock that
 * is not in the appropriate state for that operation. For example, trying
 * to unlock a lock that is not currently held by the caller.
 * </p>
 */
public class LockIllegalMonitorStateException extends AbstractRedisLockException {

    private final String currentState;
    private final String expectedState;
    private final String operation;

    /**
     * Constructs a new LockIllegalMonitorStateException with the specified details.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be null)
     * @param currentState  The current state of the lock
     * @param expectedState The expected state for the operation
     * @param operation     The operation that was attempted
     * @param message       Descriptive error message
     */
    public LockIllegalMonitorStateException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String currentState, String expectedState, String operation, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.currentState = currentState;
        this.expectedState = expectedState;
        this.operation = operation;
    }

    /**
     * Constructs a new LockIllegalMonitorStateException with the specified details and cause.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be null)
     * @param currentState  The current state of the lock
     * @param expectedState The expected state for the operation
     * @param operation     The operation that was attempted
     * @param message       Descriptive error message
     * @param cause         The underlying cause of this exception
     */
    public LockIllegalMonitorStateException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String currentState, String expectedState, String operation, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.currentState = currentState;
        this.expectedState = expectedState;
        this.operation = operation;
    }

    /**
     * Gets the current state of the lock.
     *
     * @return The current state of the lock
     */
    public String getCurrentState() {
        return currentState;
    }

    /**
     * Gets the expected state for the operation.
     *
     * @return The expected state for the operation
     */
    public String getExpectedState() {
        return expectedState;
    }

    /**
     * Gets the operation that was attempted.
     *
     * @return The operation that was attempted
     */
    public String getOperation() {
        return operation;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.state.current", currentState);
        contextMap.put("lock.state.expected", expectedState);
        contextMap.put("lock.operation", operation);
    }
}
