# Redis Locking Module: Architecture Overview

## 1. Introduction

This document outlines the comprehensive architecture for the `locking-redis-lock` module within the Destilink Framework. It provides a robust, scalable, and fault-tolerant distributed locking mechanism using Redis. The architecture emphasizes **asynchronous-first operations**, **atomicity via Lua scripts**, **strict guideline adherence**, **idempotency**, **clear configuration hierarchy**, and **robustness** as core principles.

This architecture adopts an **Asynchronous-First approach with Virtual Threads**, where all core lock operations (`lockAsync`, `tryLockAsync`, `unlockAsync`) are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads. All Redis interactions are channeled exclusively through the `redis-core` module's `ClusterCommandExecutor`, ensuring compliance with a unified Redis interaction layer.

## 2. Core Architectural Principles

The design prioritizes the following core principles:

* **Asynchronous-First Operations**: All core lock operations (`lockAsync`, `tryLockAsync`, `unlockAsync`) are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads.
* **Atomicity via Lua Scripts**: All critical Redis operations that modify lock state, TTL, or metadata are executed atomically via Lua scripts on the Redis server. This prevents race conditions and ensures data consistency.
* **Strict Guideline Adherence**: All Redis interactions are channeled exclusively through the `redis-core` module's `ClusterCommandExecutor`, ensuring compliance with a unified Redis interaction layer.
* **Idempotency**: Every mutating Redis operation is designed to be idempotent, preventing unintended side effects from client retries due to lost responses.
* **Clear Configuration Hierarchy**: Configuration properties are logically grouped, with a defined override precedence (instance-specific > bucket-level > global defaults).
* **Robustness**: Comprehensive error handling, precise TTL management, and a resilient watchdog mechanism are central to the design.
* **Virtual Thread Execution**: All lock operations leverage Virtual Threads for scalability, with immediate handoff from platform threads and proper MDC propagation.
* **Centralized Idempotency Management**: `RedisLockOperationsImpl` serves as the single point of responsibility for generating `requestUuid` per logical operation and ensuring idempotency across all mutating Lua scripts.
* **Lock-Type Semantic Isolation**: All Redis keys include mandatory lock-type segments (e.g., `reentrant`, `stamped`, `state`) to prevent cross-type interference and enable future extensibility.
* **Always-Active Watchdog**: The `LockWatchdog` service is always active when the module is enabled, with conditional per-lock monitoring based on `safetyBufferMillis` calculations.
* **Lua-Only Redis Operations**: All Redis operations related to lock state, TTL, expireAt, or any lock metadata are performed exclusively via Lua scripts to guarantee atomicity and consistency.
* **Precise TTL Management**: All expiry calculations in Lua scripts use `redis.call('TIME')` for server-side precision, with `PEXPIREAT` for absolute timestamp-based expiry.

## 3. Component Diagram

```mermaid
graph TD
    subgraph ApplicationCode ["Application Code (Virtual Thread Execution)"]
        AppLockUser["Service/User of Lock"];
    end

    subgraph LockingModuleFacade ["Locking Module Facade (Builders & Registry)"]
        LBR["LockBucketRegistry (Bean)"];
        LBB["LockBucketBuilder"];
        LCB["LockConfigBuilder"];
        ALTCB["AbstractLockTypeConfigBuilder & Subclasses"];
    end

    subgraph CoreLockingLogic ["Core Locking Logic (Async-First on Virtual Threads)"]
        direction LR;
        ARL["AbstractRedisLock<br>(Virtual Thread Dispatch, MDC Propagation)"];
        RLI["Concrete Lock Implementations<br>(RedisReentrantLock, etc.)<br>Lock-Type Specific Keys"];
        LSH["LockSemaphoreHolder<br>(CompletableFuture-based waiting)"];
    end

    subgraph SharedSpringServices ["Shared Spring-Managed Services"]
        direction LR;
        VTExec["Virtual Thread Executor<br>(newVirtualThreadPerTaskExecutor)"]
        LCR["LockComponentRegistry (Bean)"];
        SL["ScriptLoader (Bean)"];
        UMLM["UnlockMessageListenerManager (Bean)"];
        UML["UnlockMessageListener<br>(Per Bucket, VT onMessage)"];
        LW["LockWatchdog<br>(Always Active, Conditional Monitoring)"];
        ROps["RedisLockOperationsImpl<br>(Central requestUuid & Idempotency)"];
        LOS["DefaultLockOwnerSupplier<br>(canUseWatchdog validation)"];
        LERRH["RedisLockErrorHandler (Bean)"];
    end

    subgraph Configuration ["Configuration & AutoConfiguration"]
        direction LR;
        RLP["RedisLockProperties<br>(WatchdogProperties: interval, factor)<br>(Defaults: leaseTime, retryInterval, maxRetries, acquireTimeout)"];
        LBC["LockBucketConfig<br>(Programmatic Override)"];
        RAutoConfig["RedisLockAutoConfiguration"];
        RCProps["RedisCoreProperties<br>(from redis-core)"];
    end

    subgraph ExternalSystems ["External Systems & Redis Layer"]
        Redis["Redis Cluster<br>(Lua Scripts, PEXPIREAT, TIME)"];
        CCExec["ClusterCommandExecutor<br>(Exclusive Redis Interface)"];
        LuaScripts["Lua Scripts<br>(Idempotency Wrapper, Structured Returns)"];
    end

    AppLockUser --> LBR;
    LBR -- "creates" --> LBB;
    LBB --> LCB;
    LCB --> ALTCB;
    ALTCB -- ".build() creates" --> RLI;
    RLI -- "extends" --> ARL;

    ARL -- "dispatches to" --> VTExec;
    ARL -- "accesses services via" --> LCR;
    ARL -- "registers before Redis attempt" --> LSH;

    LCR -- "provides" --> SL;
    LCR -- "provides" --> UMLM;
    LCR -- "provides" --> LW;
    LCR -- "provides" --> ROps;
    LCR -- "provides" --> LOS;
    LCR -- "provides" --> LERRH;
    LCR -- "provides" --> VTExec;

    UMLM -- "manages" --> UML;
    UML -- "signals via CompletableFuture" --> LSH;
    UML -- "subscribes to unlock channels" --> Redis;

    RAutoConfig -- "defines bean" --> LBR;
    RAutoConfig -- "defines bean" --> LCR;
    RAutoConfig -- "defines bean" --> VTExec;
    RAutoConfig -- "defines bean (always active)" --> LW;
    RAutoConfig -- "defines bean" --> ROps;
    RAutoConfig -- "enables" --> RLP;
    RAutoConfig -- "uses" --> RCProps;

    RLP -- "provides global defaults" --> LBC;
    LBC -- "configures" --> LBB;

    ROps -- "loads scripts via" --> SL;
    ROps -- "executes via" --> CCExec;
    ROps -- "handles errors via" --> LERRH;
    LW -- "extends leases via" --> ROps;

    CCExec -- "executes" --> LuaScripts;
    LuaScripts -- "run on" --> Redis;

    style AppLockUser fill:#lightgrey;
    style LBR fill:#lightblue;
    style ARL fill:#adebad;
    style LCR fill:#ccffcc;
    style VTExec fill:#ccffcc;
    style RLP fill:#ffcc99;
    style Redis fill:#ffcccc;
    style LuaScripts fill:#ffffcc;
```

## 4. Key Components and Responsibilities

### 4.1 Configuration Components

* **`RedisLockProperties`**: Central configuration with nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`). Note: `responseCacheTtl` is critical for idempotency.
* **`LockBucketConfig`**: Programmatic configuration overrides for bucket-level settings.
* **`RedisLockAutoConfiguration`**: Spring auto-configuration defining all beans with explicit dependencies.

### 4.2 Shared Spring-Managed Services

* **`LockComponentRegistry`**: Central registry providing access to all shared services.
* **`ScriptLoader`**: Manages Lua script loading and caching.
* **`UnlockMessageListenerManager`**: Manages Redis Pub/Sub listeners for unlock notifications.
* **`UnlockMessageListener`**: Per-bucket listener subscribing to `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`.
* **`LockWatchdog`**: Always-active service providing conditional monitoring based on `safetyBufferMillis`.
* **`RedisLockOperationsImpl`**: Central component for `requestUuid` generation, idempotency management, and Redis operation retries.
* **`DefaultLockOwnerSupplier`**: Provides `ownerId` generation and `canUseWatchdog()` validation.
* **`RedisLockErrorHandler`**: Centralized error handling and exception mapping.
* **Virtual Thread `ExecutorService`**: Dedicated executor for all lock operations.

### 4.3 Core Locking Logic

* **`LockBucketRegistry`**: Entry point for creating lock buckets and builders.
* **`AbstractRedisLock`**: Base class implementing Virtual Thread dispatch, MDC propagation, and `LockSemaphoreHolder` registration.
* **Concrete Lock Implementations**: Type-specific implementations (e.g., `RedisReentrantLock`) using lock-type-specific Redis keys.
* **`LockSemaphoreHolder`**: Manages `CompletableFuture`-based waiting for lock acquisition.

## 5. Redis Key Schema and Semantic Isolation

All Redis keys follow the format: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`

* **Lock-Type Segments**: Mandatory segments (`reentrant`, `stamped`, `state`) ensure semantic isolation between different lock types.
* **`lockDataKey`**: Redis Hash storing metadata (`ownerId`, `originalLeaseTimeMillis`, `expiresAtMillis`).
* **Co-location**: Curly braces ensure Redis Cluster co-location for atomic multi-key operations.

## 6. Data Flow and Interactions

### 6.1 Virtual Thread Execution Flow

All lock operations follow this pattern:

1. **Immediate Handoff**: Public methods dispatch work to Virtual Thread executor
2. **MDC Propagation**: Context captured and restored in Virtual Thread
3. **Non-Blocking I/O**: All Redis operations via `ClusterCommandExecutor`
4. **Efficient Parking**: `Thread.sleep()` used for delays without blocking platform threads

### 6.2 Lock Acquisition Flow

* **Registration First**: `LockSemaphoreHolder` registered before first Redis attempt to prevent race conditions
* **Idempotent Operations**: `RedisLockOperationsImpl` generates unique `requestUuid` per logical operation
* **Pub/Sub + TTL Fallback**: Primary waiting via Redis Pub/Sub with TTL-based fallback
* **`acquireTimeout` Respect**: Non-interruption of in-flight Redis operations with result precedence

### 6.3 Watchdog Mechanism

* **Always Active**: Service runs when module is enabled
* **Conditional Monitoring**: Only monitors locks where `userLeaseTime > safetyBufferMillis` and instance-bound
* **`originalLeaseTimeMillis` Preservation**: Watchdog never modifies user's intended lease duration
* **Precise Expiry**: Uses `PEXPIREAT` with Redis server time for accuracy

### 6.4 Idempotency Implementation

* **Response Cache**: All mutating Lua scripts implement idempotency wrapper using `requestUuid`
* **Cache TTL**: Controlled by `responseCacheTtl` configuration property
* **Retry Safety**: Same `requestUuid` used for all retries of a single logical operation

## 7. Adherence to Destilink Framework Guidelines

The architecture ensures strict compliance with framework guidelines:

* **No `@ComponentScan`**: All beans explicitly defined in auto-configuration
* **Constructor Injection**: All dependencies injected via constructors
* **Unified Redis Interface**: Exclusive use of `ClusterCommandExecutor` from `redis-core`
* **Standard Configuration**: YAML-based configuration with proper property binding
* **MDC Propagation**: Context properly propagated to Virtual Threads
* **Exception Handling**: Structured exception hierarchy with proper markers