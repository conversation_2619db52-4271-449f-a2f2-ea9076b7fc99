-- Redis Watchdog Refresh Lock Script with Idempotency
-- This script atomically refreshes a lock's lease time by the watchdog with idempotency support
-- IMPORTANT: This script NEVER modifies originalLeaseTimeMillis - it only extends the current TTL
-- 
-- KEYS[1] - The main lock key (format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>})
-- KEYS[2] - The lock data key (Redis Hash for metadata)
-- KEYS[3] - The response cache key for idempotency
-- 
-- ARGV[1] - Request UUID for idempotency
-- ARGV[2] - Response cache TTL in seconds
-- ARGV[3] - Lock owner ID
-- ARGV[4] - Target absolute expiration time in milliseconds (calculated by watchdog)
-- ARGV[5] - Lock type (e.g., "reentrant", "stamped", "state")

local lockKey = KEYS[1]
local lockDataKey = KEYS[2]
local responseCacheKey = KEYS[3]

local requestUuid = ARGV[1]
local responseCacheTtl = tonumber(ARGV[2])
local ownerId = ARGV[3]
local targetExpiresAtMillis = tonumber(ARGV[4])
local lockType = ARGV[5]

-- ========================================
-- IDEMPOTENCY CHECK
-- ========================================
-- Check if this operation was already executed
local cachedResponse = redis.call('GET', responseCacheKey)
if cachedResponse then
    -- Operation already completed, return cached result
    local result = cjson.decode(cachedResponse)
    return result
end

-- ========================================
-- CORE WATCHDOG REFRESH LOGIC
-- ========================================
-- Get current Redis server time for precise calculations
local timeResult = redis.call('TIME')
local currentTimeMillis = tonumber(timeResult[1]) * 1000 + math.floor(tonumber(timeResult[2]) / 1000)

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local statusCode = 0  -- Default: not refreshed
local refreshed = false
local originalLeaseTimeMillis = 0

if currentOwner == ownerId then
    -- Lock is held by the requesting owner, refresh it
    
    -- Get the original lease time from metadata (NEVER modify this)
    local storedOriginalLeaseTime = redis.call('HGET', lockDataKey, 'originalLeaseTimeMillis')
    if storedOriginalLeaseTime then
        originalLeaseTimeMillis = tonumber(storedOriginalLeaseTime)
    end
    
    -- Set the new expiration time as calculated by the watchdog
    redis.call('PEXPIREAT', lockKey, targetExpiresAtMillis)
    
    -- Update only the current expiration time in metadata
    -- CRITICAL: Do NOT modify originalLeaseTimeMillis
    redis.call('HSET', lockDataKey, 'expiresAtMillis', targetExpiresAtMillis)
    redis.call('PEXPIREAT', lockDataKey, targetExpiresAtMillis)
    
    statusCode = 1  -- Successfully refreshed
    refreshed = true
    
elseif currentOwner == false then
    -- Lock doesn't exist (expired or never acquired)
    statusCode = 0  -- Not refreshed (lock doesn't exist)
    
else
    -- Lock is held by a different owner
    statusCode = -1  -- Not refreshed (not owner)
end

-- ========================================
-- PREPARE STRUCTURED RESPONSE
-- ========================================
local response = {
    statusCode,
    targetExpiresAtMillis,
    originalLeaseTimeMillis  -- Return the preserved original lease time
}

-- ========================================
-- CACHE RESPONSE FOR IDEMPOTENCY
-- ========================================
-- Store the response in cache for future idempotency checks
redis.call('SETEX', responseCacheKey, responseCacheTtl, cjson.encode(response))

return response
