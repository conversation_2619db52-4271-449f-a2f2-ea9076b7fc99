package com.tui.destilink.framework.locking.redis.lock.service;

import com.tui.destilink.framework.locking.redis.lock.exception.*;
import io.lettuce.core.RedisCommandExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.QueryTimeoutException;

import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;

/**
 * Handler for Redis lock-related errors.
 * <p>
 * This component provides centralized error handling for Redis lock operations,
 * including standardized logging, error classification, exception translation,
 * and Virtual Thread-aware exception propagation.
 * </p>
 * <p>
 * Key responsibilities:
 * - Exception translation from low-level Redis exceptions to structured lock exceptions
 * - Context enrichment with lock-specific information
 * - CompletableFuture exception unwrapping for Virtual Thread execution
 * - MDC context preservation during exception processing
 * </p>
 */
public class RedisLockErrorHandler {

    private static final Logger log = LoggerFactory.getLogger(RedisLockErrorHandler.class);

    /**
     * Handles errors that occur during lock acquisition.
     *
     * @param lockName  The name of the lock being acquired
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockAcquisitionError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to acquire lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during lock release.
     *
     * @param lockName  The name of the lock being released
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockReleaseError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to release lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during lock extension.
     *
     * @param lockName  The name of the lock being extended
     * @param exception The exception that occurred
     * @param lockOwner The identifier of the lock owner
     */
    public void handleLockExtensionError(String lockName, Exception exception, String lockOwner) {
        log.error("Failed to extend lock '{}' for owner '{}': {}",
                lockName, lockOwner, exception.getMessage(), exception);
    }

    /**
     * Handles errors that occur during Redis Pub/Sub operations.
     *
     * @param channel   The Redis channel involved
     * @param exception The exception that occurred
     */
    public void handlePubSubError(String channel, Exception exception) {
        log.error("Redis Pub/Sub error on channel '{}': {}",
                channel, exception.getMessage(), exception);
    }

    /**
     * Translates low-level Redis exceptions into structured lock exceptions.
     * <p>
     * This method unwraps CompletableFuture exceptions and maps Redis-specific
     * exceptions to appropriate AbstractRedisLockException subtypes with full
     * contextual information.
     * </p>
     *
     * @param exception   The original exception to translate
     * @param lockName    The full Redis key of the lock involved
     * @param lockType    The specific type of lock
     * @param lockOwnerId The ID of the owner attempting the operation
     * @param requestUuid The unique ID for the lock operation attempt
     * @param operation   The operation that was being performed
     * @return A structured lock exception with appropriate classification
     */
    public AbstractRedisLockException translateException(Throwable exception, String lockName, String lockType,
            String lockOwnerId, String requestUuid, String operation) {

        // Unwrap CompletableFuture exceptions from Virtual Thread execution
        Throwable cause = unwrapAsyncException(exception);

        // Classify and translate the exception
        if (cause instanceof RedisCommandExecutionException) {
            RedisCommandExecutionException redisEx = (RedisCommandExecutionException) cause;
            return new LockCommandException(lockName, lockType, lockOwnerId, requestUuid,
                    operation, redisEx.getMessage(),
                    "Redis command execution failed: " + redisEx.getMessage(), redisEx);
        } else if (cause instanceof DataAccessException) {
            DataAccessException dataEx = (DataAccessException) cause;
            if (dataEx instanceof QueryTimeoutException) {
                return new LockTimeoutException(lockName, lockType, lockOwnerId, requestUuid,
                        5000L, 1, "Redis operation timeout during " + operation, dataEx);
            } else {
                return new LockConnectionException(lockName, lockType, lockOwnerId, requestUuid,
                        dataEx.getMessage(), "Redis connection error during " + operation, dataEx);
            }
        } else if (cause instanceof IllegalArgumentException) {
            return new NonRetryableLockException(lockName, lockType, lockOwnerId, requestUuid,
                    cause.getMessage(), "validation",
                    "Invalid argument for " + operation, cause);
        } else if (cause instanceof InterruptedException) {
            return new LockInterruptedException(lockName, lockType, lockOwnerId, requestUuid,
                    "Operation interrupted during " + operation, (InterruptedException) cause);
        } else {
            // Default to a general lock operation exception
            return new LockOperationException(lockName, lockType, lockOwnerId, requestUuid,
                    operation, 1, 1, "Unexpected error during " + operation, cause);
        }
    }

    /**
     * Unwraps CompletableFuture exceptions to get the root cause.
     * <p>
     * This method handles the exception unwrapping that occurs when Virtual Thread
     * execution completes with exceptions, ensuring we get the actual Redis exception
     * rather than the wrapper.
     * </p>
     *
     * @param exception The exception to unwrap
     * @return The root cause exception
     */
    private Throwable unwrapAsyncException(Throwable exception) {
        Throwable current = exception;

        // Unwrap CompletionException and ExecutionException
        while (current instanceof CompletionException || current instanceof ExecutionException) {
            Throwable cause = current.getCause();
            if (cause != null) {
                current = cause;
            } else {
                break;
            }
        }

        return current;
    }

    /**
     * Classifies an exception as retryable or non-retryable.
     * <p>
     * This method determines whether an exception represents a transient error
     * that can be retried or a permanent error that should not be retried.
     * </p>
     *
     * @param exception The exception to classify
     * @return true if the exception is retryable, false otherwise
     */
    public boolean isRetryableException(Throwable exception) {
        Throwable cause = unwrapAsyncException(exception);

        // Retryable exceptions - transient errors
        if (cause instanceof QueryTimeoutException ||
            cause instanceof DataAccessException ||
            (cause instanceof RedisCommandExecutionException &&
             cause.getMessage() != null &&
             (cause.getMessage().contains("BUSY") ||
              cause.getMessage().contains("LOADING") ||
              cause.getMessage().contains("READONLY")))) {
            return true;
        }

        // Non-retryable exceptions - permanent errors
        if (cause instanceof IllegalArgumentException ||
            cause instanceof SecurityException ||
            (cause instanceof RedisCommandExecutionException &&
             cause.getMessage() != null &&
             (cause.getMessage().contains("NOSCRIPT") ||
              cause.getMessage().contains("WRONGTYPE") ||
              cause.getMessage().contains("NOAUTH")))) {
            return false;
        }

        // Default to retryable for unknown exceptions
        return true;
    }
}