-- Redis Lock Acquisition Script with Idempotency
-- This script atomically attempts to acquire a lock with idempotency support
--
-- KEYS[1] - The main lock key (format: <prefix>:<bucketName>:__locks__:<lockType>:{<lockName>})
-- KEYS[2] - The lock data key (Redis Hash for metadata)
-- KEYS[3] - The response cache key for idempotency
--
-- ARGV[1] - Request UUID for idempotency
-- ARGV[2] - Response cache TTL in seconds
-- ARGV[3] - Lock owner ID
-- ARGV[4] - Relative lease time in milliseconds (user-provided)
-- ARGV[5] - Lock type (e.g., "reentrant", "stamped", "state")

local lockKey = KEYS[1]
local lockDataKey = KEYS[2]
local responseCacheKey = KEYS[3]

local requestUuid = ARGV[1]
local responseCacheTtl = tonumber(ARGV[2])
local ownerId = ARGV[3]
local relativeLeaseTimeMillis = tonumber(ARGV[4])
local lockType = ARGV[5]

-- ========================================
-- IDEMPOTENCY CHECK
-- ========================================
-- Check if this operation was already executed
local cachedResponse = redis.call('GET', responseCacheKey)
if cachedResponse then
    -- Operation already completed, return cached result
    local result = cjson.decode(cachedResponse)
    return result
end

-- ========================================
-- CORE LOCK ACQUISITION LOGIC
-- ========================================
-- Get current Redis server time for precise calculations
local timeResult = redis.call('TIME')
local currentTimeMillis = tonumber(timeResult[1]) * 1000 + math.floor(tonumber(timeResult[2]) / 1000)

-- Calculate absolute expiration time
local expiresAtMillis = currentTimeMillis + relativeLeaseTimeMillis

-- Check current lock state
local currentOwner = redis.call('GET', lockKey)
local statusCode
local acquired = false

if currentOwner == false then
    -- Lock doesn't exist, acquire it
    redis.call('SET', lockKey, ownerId)
    redis.call('PEXPIREAT', lockKey, expiresAtMillis)

    -- Store metadata in hash
    redis.call('HSET', lockDataKey,
        'ownerId', ownerId,
        'expiresAtMillis', expiresAtMillis,
        'originalLeaseTimeMillis', relativeLeaseTimeMillis,
        'lockType', lockType)
    redis.call('PEXPIREAT', lockDataKey, expiresAtMillis)

    statusCode = -1 * expiresAtMillis  -- Negative indicates success
    acquired = true

elseif currentOwner == ownerId then
    -- Reentrant acquisition - refresh the lock
    redis.call('PEXPIREAT', lockKey, expiresAtMillis)

    -- Update metadata
    redis.call('HSET', lockDataKey,
        'expiresAtMillis', expiresAtMillis,
        'originalLeaseTimeMillis', relativeLeaseTimeMillis)
    redis.call('PEXPIREAT', lockDataKey, expiresAtMillis)

    statusCode = -1 * expiresAtMillis  -- Negative indicates success
    acquired = true

else
    -- Lock held by another owner
    local currentTtl = redis.call('PTTL', lockKey)
    if currentTtl == -1 then
        statusCode = 0  -- Lock exists but no TTL (forever)
    else
        statusCode = currentTtl  -- Return remaining TTL
    end
end

-- ========================================
-- PREPARE STRUCTURED RESPONSE
-- ========================================
local response = {
    statusCode,
    expiresAtMillis,
    relativeLeaseTimeMillis
}

-- ========================================
-- CACHE RESPONSE FOR IDEMPOTENCY
-- ========================================
-- Store the response in cache for future idempotency checks
redis.call('SETEX', responseCacheKey, responseCacheTtl, cjson.encode(response))

return response