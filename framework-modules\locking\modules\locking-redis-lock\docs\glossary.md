# Redis Locking Module: Glossary

This document defines key terms used within the `locking-redis-lock` module and its associated documentation. The glossary reflects the **Asynchronous-First approach with Virtual Threads**, **centralized idempotency management**, **refined watchdog mechanism**, and **lock-type semantic isolation** as outlined in the comprehensive architectural plan.

- **`AbstractRedisLock`**: Base Java class providing common functionality for all Redis lock implementations in the module. It implements immediate handoff to Virtual Thread executor, MDC propagation, and centralized component access via `LockComponentRegistry`. No longer contains `requestUuid` field - this is now managed centrally by `RedisLockOperationsImpl`.

- **Application-Instance-Bound Lock**: A lock whose ownership is tied to a specific application instance (e.g., a specific pod ID). These locks are eligible for watchdog lease renewal if their user-provided lease time exceeds the `safetyBufferMillis` and `LockOwnerSupplier.canUseWatchdog()` returns true.

- **Asynchronous-First Operations**: Core design principle where all lock operations (`lockAsync`, `tryLockAsync`, `unlockAsync`) are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads. Synchronous methods are blocking wrappers that call `CompletableFuture.get()`.

- **`AsyncLock`**: Interface extending `java.util.concurrent.locks.Lock` that provides non-blocking, `CompletableFuture`-based asynchronous versions of standard lock operations. All concrete lock implementations must implement this interface.

- **Atomic Operation**: Operation guaranteed to execute fully without interruption, ensured by executing commands as Lua scripts on the Redis server. All atomic operations are executed exclusively via `redis-core`'s `ClusterCommandExecutor`.

- **Bucket (Lock Bucket)**: Logical grouping or namespace for locks allowing common default configurations. Configured programmatically via builders with override precedence: instance-specific > bucket-level > global defaults.

- **Builder API**: Fluent API starting with `LockBucketRegistry.builder(...)` for configuring and creating specific lock instances with programmatic overrides.

- **`ClusterCommandExecutor`**: Critical component from `redis-core` module used exclusively for all Redis command executions. Provides asynchronous, cluster-aware command execution ensuring framework compliance.

- **Distributed Lock**: Synchronization primitive for coordinating access to shared resources among multiple processes or services in a distributed environment.

- **`expiresAtMillis`**: Absolute Unix timestamp (in milliseconds) when a lock is currently set to expire in Redis, calculated by Redis server using `redis.call('TIME')` and `PEXPIREAT`. Stored in `lockDataKey` and reflects actual expiry time of `lockKey`.

- **Hash Tag (Redis Cluster)**: Mechanism using curly braces (`{...}`) in Redis key names to ensure multiple keys are allocated to the same hash slot for atomic multi-key Lua script operations. The lock identifier (e.g., `{order123}`) serves this purpose.

- **Idempotency Wrapper**: Mandatory pattern applied to all mutating Lua scripts. Checks Redis response cache using `requestUuid` before executing core logic, and stores operation result in cache with `responseCacheTtl` TTL upon completion. Prevents duplicate execution of retried operations.

- **`lockDataKey`**: Secondary Redis key (typically a Redis Hash) storing additional metadata associated with a `lockKey`. Includes `ownerId`, `originalLeaseTimeMillis` (user's intended full lease duration), and `expiresAtMillis` (current absolute expiry timestamp). Key format includes mandatory lock-type segment (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}:data`).

- **`lockKey`**: Primary Redis key for the distributed lock itself (e.g., `myApp:__lock_buckets__:orders:__locks__:<lockType>:{order123}`). Holds `ownerId` as value (or is a Hash for reentrant/complex locks) with expiry managed by `PEXPIREAT`. Includes mandatory lock-type segment for semantic isolation.

- **Lock-Type Segment**: Mandatory segment within Redis keys (e.g., `reentrant`, `stamped`, `state`, `readwrite`) that semantically isolates different lock types, preventing cross-type interference and enabling future extensibility. Part of both `lockKey` and `lockDataKey`.

- **`originalLeaseTimeMillis`**: The `relativeLeaseTimeMillis` value that was last explicitly set by a user-initiated lock acquisition or extension. Stored persistently in Redis (`lockDataKey`) and used by `LockWatchdog` to determine intended full lease duration for renewal cycles. **Never** modified by the watchdog itself.

- **`ownerId`**: Unique identifier for the lock holder (e.g., `default-instance-id-1:thread-123`). Identifies which application instance and thread/process holds the lock. Used for watchdog eligibility determination via `LockOwnerSupplier.canUseWatchdog()`.

- **`relativeLeaseTimeMillis`**: Duration (in milliseconds) requested by the user for a lock's lease. This is a relative value (e.g., "30 seconds from now") and serves as input to lock acquisition and extension operations. Becomes `originalLeaseTimeMillis` when stored in Redis.

- **`requestUuid`**: Unique identifier (UUID) generated per logical operation by `RedisLockOperationsImpl` (e.g., a single `tryLock` call, including its internal retries). Used for the idempotency mechanism with response cache. Same UUID used for all retry attempts of a single logical operation.

- **Response Cache**: Centralized Redis-based cache system storing results of mutating lock operations, keyed by `requestUuid`. Managed entirely within Lua scripts (Idempotency Wrapper) with TTL controlled by `responseCacheTtl`. Foundation for idempotency mechanism.

- **`safetyBufferMillis`**: Calculated duration (`watchdog.interval * watchdog.factor`) used by `LockWatchdog`. Determines minimum `userLeaseTime` for watchdog eligibility and target TTL the watchdog maintains for eligible locks until final leg of user's intended lease.

- **Virtual Threads**: Lightweight threads provided by the JVM (Project Loom) used by this module to execute all lock operations asynchronously, preventing platform thread blockage and improving scalability. MDC context is propagated to these threads for consistent logging.

- **`LockWatchdog`**: Spring-managed bean that periodically extends the lease (TTL using `PEXPIREAT`) of active, application-instance-bound locks in Redis. Always active when module is enabled, but only monitors locks where `userProvidedLeaseTime > safetyBufferMillis` and are instance-bound. **Never** modifies `originalLeaseTimeMillis` stored in `lockDataKey`.