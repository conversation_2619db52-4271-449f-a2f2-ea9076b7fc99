# Redis Locking Module: Implementation Details

## 1. Introduction

This document provides comprehensive implementation specifics for key components and mechanisms within the `locking-redis-lock` module. Understanding these details is crucial for developing, debugging, and effectively utilizing the module. The implementation emphasizes **Asynchronous-First Operations**, **Lua-Only Redis Operations**, **Centralized Idempotency Management**, **Virtual Thread Integration**, and **Lock-Type Semantic Isolation**.

### 1.1 Core Implementation Principles

* **Asynchronous-First Operations**: All core lock operations are designed to be non-blocking, leveraging Java's `CompletableFuture` and Virtual Threads
* **Atomicity via Lua Scripts**: All critical Redis operations that modify lock state, TTL, or metadata are executed atomically via Lua scripts
* **Centralized Idempotency**: `RedisLockOperationsImpl` serves as the single point for `requestUuid` generation and idempotency management
* **Virtual Thread Integration**: Complete adoption with immediate handoff from platform threads and proper MDC propagation
* **Lock-Type Semantic Isolation**: All Redis keys include mandatory lock-type segments for preventing cross-type interference
* **Lua-Only Redis Operations**: All lock state, TTL, and metadata changes exclusively via Lua scripts for atomicity and consistency

### 1.2 Redis Key Construction Requirements

All Redis keys MUST be constructed using `redis-core` module utilities (`AbstractRedisKey`, `RedisKey`, `RedisKeyPrefix`) and MUST include mandatory lock-type segments (e.g., `reentrant`, `stamped`, `state`) following the format: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`.

## 2. Core Components and Responsibilities

The module is structured around several core components that collaborate to provide distributed locking functionality with Virtual Thread integration and centralized idempotency management:

### 2.1 Configuration and Auto-Configuration

* **`RedisLockAutoConfiguration`** (Spring `@AutoConfiguration`):
  - Module's entry point for Spring Boot with strict framework compliance
  - **No `@ComponentScan`**: All framework beans explicitly defined via `@Bean` methods
  - Conditionally enables based on `destilink.fw.locking.redis.enabled` property
  - Instantiates Virtual Thread `ExecutorService` and all shared service beans
  - Registered in `META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports`

* **`RedisLockProperties`** (Spring `@ConfigurationProperties`):
  - Binds global configurations from YAML files (`destilink.fw.locking.redis.*`)
  - Contains nested `WatchdogProperties` (`interval`, `factor`) and `Defaults` (`leaseTime`, `retryInterval`, `maxRetries`, `acquireTimeout`)
  - Includes critical `responseCacheTtl` property for idempotency mechanism
  - **Removed properties**: `pubSubWaitTimeout`, `lockOwnerIdValidationRegex`, `max*Length` properties, `fairLockBehavior`, `asyncExecutorName`, `redisOperationTimeout`
  - Works with `RedisCoreProperties` for consistent Redis client configuration

### 2.2 Central Registry and Component Management

* **`LockComponentRegistry`** (Spring Bean):
  - Central registry providing access to all shared services
  - Injected into lock builders and other components for dependency management
  - Provides: `ScriptLoader`, `UnlockMessageListenerManager`, `LockWatchdog`, `RedisLockOperations`, `DefaultLockOwnerSupplier`, `RedisLockErrorHandler`, `LockMonitor`, Virtual Thread `ExecutorService`
  - Simplifies dependency injection and ensures consistent service access

* **`LockBucketRegistry`** (Spring Bean):
  - Primary factory for initiating lock creation process
  - Entry point to fluent builder API
  - Injects `LockComponentRegistry` and initialized `LockBucketConfig` into builders

### 2.3 Configuration Management

* **`LockBucketConfig`** (Non-Spring managed POJO):
  - Holds resolved and effective configuration for specific lock bucket
  - Instantiated by `LockBucketBuilder` with global defaults from `RedisLockProperties.Defaults`
  - Supports programmatic overrides with clear precedence hierarchy
  - Determines watchdog eligibility based on configured `LockOwnerSupplier` and conditions

* `LockBucketConfig` (Non-Spring managed POJO):
  * Holds the *resolved* and *effective* configuration for a specific lock bucket.
  * Instantiated by `LockBucketBuilder`, initialized with global defaults from `RedisLockProperties.Defaults` and potentially from `RedisCoreProperties`.
  * Bucket-level defaults can be overridden programmatically. Watchdog eligibility for locks within the bucket is determined by the configured `LockOwnerSupplier` and other global/instance conditions.

* `LockBucketRegistry` (Spring Bean):
  * The primary factory for initiating the lock creation process.
  * Provides the entry point to the fluent builder API.
  * Injects the `LockComponentRegistry` and an initialized `LockBucketConfig` into the `LockBucketBuilder`.

### 2.4 Builder Chain and Lock Creation

* **Builder Chain** (`LockBucketBuilder` -> `LockConfigBuilder` -> `AbstractLockTypeConfigBuilder` & Subclasses):
  - Configures bucket-name, scope, custom `LockOwnerSupplier`
  - Provides overrides for `defaults.leaseTime`, `defaults.retryInterval`, `defaults.maxRetries`, `defaults.acquireTimeout`, `stateKeyExpiration`
  - `LockOwnerSupplier.canUseWatchdog()` and `leaseTime` relative to `safetyBufferMillis` determine watchdog eligibility

### 2.5 Core Lock Implementation

* **`AbstractRedisLock`** (Base Class):
  - **Virtual Thread Integration**: Implements immediate handoff pattern - all public methods dispatch work to Virtual Thread executor
  - **Asynchronous Operations**: Provides `lockAsync()`, `tryLockAsync()`, `unlockAsync()`, `extendLeaseAsync()`
  - **Blocking Wrappers**: Synchronous `Lock` methods implemented as blocking wrappers calling `CompletableFuture.get()`
  - **MDC Propagation**: Captures and restores MDC context in Virtual Threads
  - **Component Access**: Uses `LockComponentRegistry` for accessing shared services (no direct field injection)
  - **Semaphore Registration**: Ensures `LockSemaphoreHolder` registered **before** first Redis attempt
  - **No requestUuid Field**: Removed - now managed centrally by `RedisLockOperationsImpl`
  - **Watchdog Integration**: Manages conditional watchdog registration based on eligibility criteria

* **Concrete Lock Implementations**:
  - **All extend `AbstractRedisLock` and implement `AsyncLock` interface**
  - **Lock-Type Specific Keys**: Each uses specific lock-type segment for semantic isolation
  - **`RedisReentrantLock`**: Uses `reentrant` segment, manages reentrancy in Redis Hash
  - **`RedisStateLock`**: Uses `state` segment, manages associated state values
  - **`RedisStampedLock`**: Uses `stamped` segment, provides optimistic read capabilities
  - **`RedisReadWriteLock`**: Uses `readwrite` segment, contains inner `ReadLock` and `WriteLock` extending `AbstractRedisLock`

### 2.6 Supporting Services

* **`ScriptLoader`** (Spring Bean):
  - Loads all necessary Lua scripts from classpath during application startup
  - Caches scripts for efficient reuse across all lock operations

* **`RedisLockOperationsImpl`** (Spring Bean - Central Idempotency Manager):
  - **Exclusive Redis Interface**: Uses only `ClusterCommandExecutor` for all Redis operations
  - **Centralized Idempotency Management**:
    - Generates unique `requestUuid` per logical lock operation (e.g., single `tryLock` call)
    - Uses same `requestUuid` for all internal retries of that logical operation
    - Passes `requestUuid` and `responseCacheTtl` to all mutating Lua scripts
  - **Structured Response Handling**: Parses Lua script responses (status, `expiresAtMillis`, `originalLeaseTimeMillis`)
  - **Internal Retry Logic**: Implements retry for individual Redis operations based on `maxRetries` and `retryInterval`

* **`DefaultLockOwnerSupplier`** (Spring Bean):
  - Generates unique `ownerId`s for lock ownership identification
  - Implements `canUseWatchdog()` for determining watchdog eligibility

* **`RedisLockErrorHandler`** (Spring Bean):
  - Centralizes exception translation from Redis operations
  - Maps low-level `ClusterCommandExecutor` exceptions to specific `AbstractRedisLockException` subtypes

### 2.7 Messaging and Waiting Infrastructure

* **`UnlockMessageListenerManager`** (Spring Bean):
  - Manages lifecycle of `UnlockMessageListener` instances (typically one per bucket)

* **`UnlockMessageListener`** (Per Bucket):
  - Implements `MessageListener` for Redis Pub/Sub
  - Subscribes to bucket-specific channels: `<prefix>:<bucketName>:__unlock_channels__:<lockType>:*`
  - Derives `lockName` and `lockType` from channel name, parses `UnlockType` from message
  - Signals `LockSemaphoreHolder` to complete waiting `CompletableFuture`s
  - Processes messages on Virtual Thread executor

* **`LockSemaphoreHolder`** (Non-Spring managed, per `lockKey`):
  - Associated with each `lockKey` having waiting `CompletableFuture`s
  - Managed within per-bucket `UnlockMessageListener`
  - Facilitates completion of `CompletableFuture`s when unlock notifications received

### 2.8 Watchdog Service

* **`LockWatchdog`** (Spring Bean - Always Active):
  - **Always Active**: Runs when module is enabled, provides conditional per-lock monitoring
  - **Eligibility Criteria**: Only monitors locks where `userProvidedLeaseTime > safetyBufferMillis` and instance-bound
  - **Lease Extension**: Periodically executes `watchdog_refresh_lock.lua` via `RedisLockOperations`
  - **Precise TTL Management**: Uses `PEXPIREAT` with Redis server time for accuracy
  - **Preservation**: Never modifies `originalLeaseTimeMillis` stored in `lockDataKey`
  - **Configuration**: Uses `watchdog.interval` for scheduling and `watchdog.factor` for `safetyBufferMillis` calculation

* **`LockMonitor`** (Spring Bean, Optional):
  - Collects and exposes metrics related to lock operations using Micrometer

### 2.A RedisReadWriteLock

*   **Key Characteristics:**
    *   Inner `ReadLock` and `WriteLock` extend `AbstractRedisLock` and implement `AsyncLock`.
    *   Reentrancy for reads and writes managed in a main Redis Hash (`lockKey` with `readwrite` type segment).
    *   Uses `Individual Read Lock Timeout Key`s (Redis Strings with TTL, also with `readwrite` type segment) for each reentrant read acquisition.
    *   Main lock Hash's TTL dynamically managed by Lua scripts based on its own lease (potentially watchdog-managed to `safetyBufferMillis` or `userIntendedExpireTimeMillis`) and active individual read lock timeouts.
*   **Operational Details (Async-First with Virtual Threads):**
    *   `tryAcquireLockAsync` for read/write locks calls respective Lua scripts (`try_read_lock.lua`, `try_write_lock.lua`) via `RedisLockOperations`.
    *   Lua scripts manage reentrancy counts, `mode`, `ownerId` in the main Hash, create/delete individual read timeout keys, and update `expiresAtMillis` and `originalLeaseTimeMillis` in `lockDataKey`.
    *   Watchdog can manage the main `RedisReadWriteLock` instance if eligible.

### 2.B RedisStampedLock

*   **Key Characteristics:**
    *   Uses a Redis Hash (`lockKey` with `stamped` type segment) to store `version`, `write_owner_id`, `read_holders`, etc.
    *   String-based stamps.
    *   Asynchronous operations for write, read, optimistic read, and conversions, all via Lua scripts and `RedisLockOperations`.
    *   Watchdog can manage write locks if eligible.

### 2.X Asynchronous Lock Operations (`AsyncLock` Interface)

*   **Purpose**: Provides non-blocking API returning `CompletableFuture`s, executed on Virtual Threads.
*   **Integration**: All concrete lock implementations implement `AsyncLock`.
*   **Waiting Mechanism**: For `lockAsync()`, `CompletableFuture`s are registered with `LockSemaphoreHolder` and completed by `UnlockMessageListener` or timeout.

## 3. Key Implementation Mechanisms

### 3.1 Virtual Thread Execution Model

All lock operations follow the Virtual Thread execution pattern:

1. **Immediate Handoff**: Public methods capture MDC context and dispatch work to Virtual Thread executor
2. **Non-Blocking I/O**: All Redis operations via `ClusterCommandExecutor` are non-blocking
3. **Efficient Parking**: `Thread.sleep()` used for delays without blocking platform threads
4. **Context Propagation**: MDC context properly restored in Virtual Threads for consistent logging

### 3.2 Centralized Idempotency Mechanism

`RedisLockOperationsImpl` serves as the single point for idempotency management:

* **`requestUuid` Generation**: Unique UUID per logical operation (e.g., single `tryLock` call)
* **Retry Consistency**: Same `requestUuid` used for all internal retries of a logical operation
* **Response Caching**: All mutating Lua scripts implement idempotency wrapper pattern
* **Cache TTL**: Controlled by `responseCacheTtl` configuration property

### 3.3 Lock-Type Semantic Isolation

All Redis keys include mandatory lock-type segments:

* **Key Format**: `<prefix>:<bucketName>:__locks__:<lockType>:{<lockName>}`
* **Type Segments**: `reentrant`, `stamped`, `state`, `readwrite` for semantic isolation
* **Cross-Type Prevention**: Prevents interference between different lock types
* **Future Extensibility**: Enables addition of new lock types without conflicts

### 3.4 Always-Active Watchdog with Conditional Monitoring

* **Service State**: `LockWatchdog` always active when module enabled
* **Monitoring Criteria**: Only monitors locks where `userLeaseTime > safetyBufferMillis` and instance-bound
* **Lease Extension**: Uses `watchdog_refresh_lock.lua` with `PEXPIREAT` for precise timing
* **Preservation**: Never modifies `originalLeaseTimeMillis` - maintains user intent

### 3.5 Lua-Only Redis Operations

All Redis operations that modify lock state, TTL, or metadata are performed exclusively via Lua scripts:

* **Atomicity**: Ensures atomic execution of complex multi-key operations
* **Consistency**: Prevents race conditions and data corruption
* **Server-Side Logic**: Reduces network round-trips and improves performance
* **Idempotency**: All mutating scripts implement mandatory idempotency wrapper

### 3.6 Cross-Reference to Detailed Documentation

* **Lock Acquisition**: See [Lock Acquisition Mechanism](lock_acquisition.md) for Virtual Thread flow and `LockSemaphoreHolder` registration
* **Messaging**: See [Unlock Messaging](messaging.md) for Pub/Sub integration and `UnlockType` handling
* **Watchdog**: See [Watchdog Mechanism](watchdog.md) for conditional monitoring and lease extension details
* **Exception Handling**: See [Exception Handling](exception_handling.md) for Virtual Thread exception propagation
* **Configuration**: See [Configuration](configuration.md) for property hierarchy and override precedence
