package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception thrown when lock acquisition fails due to timeout.
 * <p>
 * This exception is thrown when a lock acquisition operation times out
 * after the specified acquire timeout period has elapsed. It provides
 * context about the timeout duration and number of attempts made.
 * </p>
 */
public class LockAcquisitionTimeoutException extends AbstractRedisLockException {

    private final long timeoutMillis;
    private final int attemptCount;

    /**
     * Constructs a new LockAcquisitionTimeoutException with the specified details.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be null)
     * @param timeoutMillis The timeout duration in milliseconds
     * @param attemptCount  The number of acquisition attempts made
     * @param message       Descriptive error message
     */
    public LockAcquisitionTimeoutException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long timeoutMillis, int attemptCount, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.timeoutMillis = timeoutMillis;
        this.attemptCount = attemptCount;
    }

    /**
     * Constructs a new LockAcquisitionTimeoutException with the specified details and cause.
     *
     * @param lockName      The full Redis key of the lock involved
     * @param lockType      The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId   The ID of the owner attempting the operation (can be null)
     * @param requestUuid   The unique ID for the lock operation attempt (can be null)
     * @param timeoutMillis The timeout duration in milliseconds
     * @param attemptCount  The number of acquisition attempts made
     * @param message       Descriptive error message
     * @param cause         The underlying cause of this exception
     */
    public LockAcquisitionTimeoutException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            long timeoutMillis, int attemptCount, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.timeoutMillis = timeoutMillis;
        this.attemptCount = attemptCount;
    }

    /**
     * Gets the timeout duration in milliseconds.
     *
     * @return The timeout duration in milliseconds
     */
    public long getTimeoutMillis() {
        return timeoutMillis;
    }

    /**
     * Gets the number of acquisition attempts made.
     *
     * @return The number of acquisition attempts made
     */
    public int getAttemptCount() {
        return attemptCount;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.timeout.millis", timeoutMillis);
        contextMap.put("lock.attempt.count", attemptCount);
    }
}
