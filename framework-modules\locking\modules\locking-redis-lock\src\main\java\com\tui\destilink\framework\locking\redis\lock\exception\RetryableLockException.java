package com.tui.destilink.framework.locking.redis.lock.exception;

import java.util.Map;

/**
 * Exception indicating a transient error that can be retried.
 * <p>
 * This exception is used to classify errors that are likely to be temporary
 * and may succeed if retried. Examples include temporary network issues,
 * Redis server busy conditions, or connection timeouts.
 * </p>
 * <p>
 * When this exception is thrown, the RedisLockOperationsImpl will attempt
 * to retry the operation according to the configured retry policy.
 * </p>
 */
public class RetryableLockException extends AbstractRedisLockException {

    private final String retryReason;
    private final int currentAttempt;
    private final int maxAttempts;

    /**
     * Constructs a new RetryableLockException with the specified details.
     *
     * @param lockName       The full Redis key of the lock involved
     * @param lockType       The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId    The ID of the owner attempting the operation (can be null)
     * @param requestUuid    The unique ID for the lock operation attempt (can be null)
     * @param retryReason    The reason why this operation can be retried
     * @param currentAttempt The current attempt number
     * @param maxAttempts    The maximum number of attempts allowed
     * @param message        Descriptive error message
     */
    public RetryableLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String retryReason, int currentAttempt, int maxAttempts, String message) {
        super(lockName, lockType, lockOwnerId, requestUuid, message);
        this.retryReason = retryReason;
        this.currentAttempt = currentAttempt;
        this.maxAttempts = maxAttempts;
    }

    /**
     * Constructs a new RetryableLockException with the specified details and cause.
     *
     * @param lockName       The full Redis key of the lock involved
     * @param lockType       The specific type of lock (e.g., "RedisReentrantLock")
     * @param lockOwnerId    The ID of the owner attempting the operation (can be null)
     * @param requestUuid    The unique ID for the lock operation attempt (can be null)
     * @param retryReason    The reason why this operation can be retried
     * @param currentAttempt The current attempt number
     * @param maxAttempts    The maximum number of attempts allowed
     * @param message        Descriptive error message
     * @param cause          The underlying cause of this exception
     */
    public RetryableLockException(String lockName, String lockType, String lockOwnerId, String requestUuid,
            String retryReason, int currentAttempt, int maxAttempts, String message, Throwable cause) {
        super(lockName, lockType, lockOwnerId, requestUuid, message, cause);
        this.retryReason = retryReason;
        this.currentAttempt = currentAttempt;
        this.maxAttempts = maxAttempts;
    }

    /**
     * Gets the reason why this operation can be retried.
     *
     * @return The retry reason
     */
    public String getRetryReason() {
        return retryReason;
    }

    /**
     * Gets the current attempt number.
     *
     * @return The current attempt number
     */
    public int getCurrentAttempt() {
        return currentAttempt;
    }

    /**
     * Gets the maximum number of attempts allowed.
     *
     * @return The maximum number of attempts allowed
     */
    public int getMaxAttempts() {
        return maxAttempts;
    }

    @Override
    protected void populateSpecificMarkers(Map<String, Object> contextMap) {
        contextMap.put("lock.retry.reason", retryReason);
        contextMap.put("lock.retry.current", currentAttempt);
        contextMap.put("lock.retry.max", maxAttempts);
    }
}
