package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.AsyncLock;
import com.tui.destilink.framework.locking.redis.lock.AsyncReadWriteLock;
import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;

/**
 * Redis-based implementation of a ReadWriteLock.
 * <p>
 * This implementation provides a pair of associated locks, one for read-only
 * operations ({@link RedisReadLock}) and one for writing
 * ({@link RedisWriteLock}).
 * The read lock may be held simultaneously by multiple reader threads, so long
 * as
 * there is no writer. The write lock is exclusive.
 * </p>
 */
@Slf4j
public class RedisReadWriteLock implements AsyncReadWriteLock {

    private final RedisReadLock readLock;
    private final RedisWriteLock writeLock;

    /**
     * Creates a new Redis-based ReadWriteLock with the specified key and default
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this ReadWriteLock in
     *                            Redis
     */
    public RedisReadWriteLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        this(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                properties.getDefaults().getLeaseTime().toMillis(),
                properties.getDefaults().getRetryInterval().toMillis(),
                properties.getDefaults().getMaxRetries(),
                virtualThreadExecutor, watchdog);
    }

    /**
     * Creates a new Redis-based ReadWriteLock with the specified key and custom
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this ReadWriteLock in
     *                            Redis
     * @param lockTtlMillis       Time-to-live for the lock in milliseconds
     * @param retryIntervalMillis Interval between retry attempts in milliseconds
     * @param maxRetries          Maximum number of retry attempts
     * @param virtualThreadExecutor Virtual Thread executor for async operations
     * @param watchdog            Lock watchdog for lease extension
     */
    public RedisReadWriteLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        this.readLock = new RedisReadLock(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                lockTtlMillis, retryIntervalMillis, maxRetries, virtualThreadExecutor, watchdog);
        this.writeLock = new RedisWriteLock(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                lockTtlMillis, retryIntervalMillis, maxRetries, virtualThreadExecutor, watchdog);
        log.debug("Created RedisReadWriteLock for key: {}", lockKey);
    }

    @Override
    public AsyncLock readLock() { // Return type changed to AsyncLock
        return readLock;
    }

    @Override
    public AsyncLock writeLock() { // Return type changed to AsyncLock
        return writeLock;
    }
}
